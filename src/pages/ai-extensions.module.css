/* Hero Section */
.hero {
  padding: 4rem 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.heroRow {
  display: flex;
  align-items: center;
  gap: 3rem;
  min-height: 500px;
}

.heroContent {
  flex: 1;
  z-index: 2;
}

.heroBadge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
}

.heroContent h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.heroSubtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.btnPrimary {
  background: white;
  color: #667eea;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btnPrimary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  color: #667eea;
  text-decoration: none;
}

.btnSecondary {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btnSecondary:hover {
  background: white;
  color: #667eea;
  text-decoration: none;
}

.heroStats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.heroStat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.heroStatNumber {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.heroStatLabel {
  font-size: 0.875rem;
  opacity: 0.8;
}

.heroImageContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImageWrapper {
  position: relative;
  max-width: 600px;
  width: 100%;
}

.heroImage {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.heroImage:hover {
  transform: scale(1.02);
}

/* Benefits Section */
.benefitsSection {
  padding: 5rem 0;
  background: #f8f9fa;
}

.sectionHeading {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.sectionDescription {
  font-size: 1.125rem;
  color: #6c757d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.benefitCard {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.benefitCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.benefitIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.benefitTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.benefitDescription {
  color: #6c757d;
  line-height: 1.6;
}

/* Overview Section */
.overviewSection {
  padding: 5rem 0;
  background: white;
}

.extensionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.extensionCard {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.extensionCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

.extensionImageWrapper {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.extensionImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.extensionImage:hover {
  transform: scale(1.05);
}

.extensionBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.extensionContent {
  padding: 2rem;
}

.extensionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.extensionIcon {
  font-size: 1.75rem;
  display: inline-block;
}

.extensionSubtitle {
  font-size: 1rem;
  color: #667eea;
  margin-bottom: 1rem;
  font-weight: 500;
}

.extensionDescription {
  color: #6c757d;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.extensionFeatures {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.extensionFeature {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  color: #495057;
}

.featureIcon {
  color: #28a745;
  margin-right: 0.75rem;
  font-weight: bold;
}

.extensionButtons {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.extensionButtonPrimary {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  flex: 1;
  text-align: center;
}

.extensionButtonPrimary:hover {
  background: #5a6fd8;
  text-decoration: none;
  color: white;
  transform: translateY(-2px);
}

.extensionButtonSecondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  flex: 1;
  text-align: center;
}

.extensionButtonSecondary:hover {
  background: #667eea;
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
}

/* Comparison Section */
.comparisonSection {
  padding: 5rem 0;
  background: #f8f9fa;
}

.comparisonTable {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-top: 3rem;
}

.comparisonHeader {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  background: #667eea;
  color: white;
  font-weight: 600;
}

.comparisonHeader > div {
  padding: 1.5rem 1rem;
  text-align: center;
}

.comparisonHeader > div:first-child {
  text-align: left;
}

.comparisonRow {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  border-bottom: 1px solid #e9ecef;
}

.comparisonRow:last-child {
  border-bottom: none;
}

.comparisonFeature {
  padding: 1.5rem 1rem;
  font-weight: 500;
  color: #2c3e50;
}

.comparisonCell {
  padding: 1.5rem 1rem;
  text-align: center;
  font-size: 1.25rem;
}

.comparisonNote {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #e3f2fd;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.comparisonNote p {
  margin: 0;
  color: #1565c0;
  font-style: italic;
}

/* Extension Showcase Cards */
.showcaseCard {
  width: 440px;
  height: 280px;
  border-radius: 16px;
  padding: 2rem;
  color: white;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin: 0 auto;
  max-width: 100%;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.showcaseCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.showcaseCardDecoration {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  opacity: 0.6;
}

.showcaseCardContent {
  position: relative;
  z-index: 2;
}

.showcaseCardHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.showcaseCardIcon {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.showcaseCardIcon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.showcaseCardTitle {
  color: white;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  flex: 1;
}

.showcaseCardSubtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.showcaseCardDescription {
  font-size: 1rem;
  opacity: 0.85;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.showcaseCardButton {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  color: white;
  text-decoration: none;
}

.showcaseCardButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

/* Feature Cards */
.featureCard {
  width: 100%;
  max-width: 1400px;
  height: 560px;
  border-radius: 20px;
  padding: 3rem;
  color: white;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin: 0 auto;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.featureCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
}

.featureCardDecoration {
  position: absolute;
  top: -100px;
  right: -100px;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  opacity: 0.4;
}

.featureCardContent {
  display: flex;
  height: 100%;
  gap: 3rem;
  position: relative;
  z-index: 2;
}

.featureCardLeft {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.featureCardRight {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.featureCardHeader {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.featureCardIcon {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.featureCardIcon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.featureCardTitle {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.featureCardTagline {
  font-size: 1.3rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 500;
}

.featureCardDescription {
  font-size: 1.2rem;
  opacity: 0.85;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 500px;
}

.featureCardFeatures {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2.5rem;
}

.featureCardFeature {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 500;
}

.featureCardFeatureIcon {
  color: rgba(255, 255, 255, 0.9);
  margin-right: 1rem;
  font-weight: bold;
  font-size: 1.2rem;
}

.featureCardButton {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  color: white;
  text-decoration: none;
  align-self: flex-start;
}

.featureCardButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
  border-color: rgba(255, 255, 255, 0.5);
}

.featureCardVisual {
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.featureCardVisualIcon {
  width: 120px;
  height: 120px;
  opacity: 0.6;
}

.featureCardVisualIcon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  /* filter: brightness(0) invert(1); */
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroRow {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .heroContent h1 {
    font-size: 2.5rem;
  }

  .heroButtons {
    justify-content: center;
  }

  .benefitsGrid {
    grid-template-columns: 1fr;
  }

  .extensionsGrid {
    grid-template-columns: 1fr;
  }

  .showcaseCard {
    width: 100%;
    max-width: 350px;
    height: 260px;
    padding: 1.5rem;
  }

  .showcaseCardHeader {
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .showcaseCardIcon {
    width: 36px;
    height: 36px;
  }

  .showcaseCardTitle {
    font-size: 1.2rem;
  }

  .showcaseCardSubtitle {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .showcaseCardDescription {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .showcaseCardButton {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }

  .featureCard {
    height: auto;
    min-height: 400px;
    padding: 2rem;
  }

  .featureCardContent {
    flex-direction: column;
    gap: 2rem;
  }

  .featureCardHeader {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .featureCardIcon {
    width: 60px;
    height: 60px;
  }

  .featureCardTitle {
    font-size: 1.8rem;
  }

  .featureCardTagline {
    font-size: 1.1rem;
  }

  .featureCardDescription {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    max-width: none;
  }

  .featureCardFeatures {
    margin-bottom: 2rem;
  }

  .featureCardFeature {
    font-size: 1rem;
  }

  .featureCardButton {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .featureCardVisual {
    width: 100%;
    height: 200px;
    max-width: 300px;
    margin: 0 auto;
  }

  .featureCardVisualIcon {
    width: 80px;
    height: 80px;
  }

  .comparisonHeader,
  .comparisonRow {
    grid-template-columns: 1fr;
  }

  .comparisonHeader > div,
  .comparisonFeature,
  .comparisonCell {
    text-align: left;
    border-bottom: 1px solid #e9ecef;
  }

  .comparisonHeader > div:last-child,
  .comparisonRow > div:last-child {
    border-bottom: none;
  }

  .extensionButtons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .extensionButtonPrimary,
  .extensionButtonSecondary {
    padding: 0.75rem 1rem;
  }
}
