{"homepage.socialProof.title": {"message": "Trusted by Students and Professionals Worldwide"}, "homepage.socialProof.description": {"message": "Join thousands of professionals who use FunBlocks AI to enhance their productivity and creativity"}, "homepage.socialProof.users": {"message": "Active Users"}, "homepage.socialProof.productivity": {"message": "Productivity Increase"}, "homepage.socialProof.countries": {"message": "Countries"}, "homepage.socialProof.rating": {"message": "User Rating"}, "homepage.socialProof.companies": {"message": "Used by innovative teams at"}, "homepage.comparison.title": {"message": "How FunBlocks Compares"}, "homepage.comparison.description": {"message": "FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"}, "homepage.comparison.featureHeader": {"message": "Feature"}, "homepage.comparison.funblocksHeader": {"message": "FunBlocks AI"}, "homepage.comparison.chatbotsHeader": {"message": "AI Chatbots (e.g. ChatGPT)"}, "homepage.comparison.notionHeader": {"message": "Notion"}, "homepage.comparison.mindmapHeader": {"message": "Mind Map Tools"}, "homepage.comparison.feature1": {"message": "All-in-One AI Workspace"}, "homepage.comparison.feature2": {"message": "Visual Thinking & Mind Mapping"}, "homepage.comparison.feature3": {"message": "AI-Powered Documents"}, "homepage.comparison.feature4": {"message": "AI Slide Generation"}, "homepage.comparison.feature5": {"message": "Infographic Creation"}, "homepage.comparison.feature6": {"message": "Multi-Model AI Support"}, "homepage.comparison.feature7": {"message": "Thinking Frameworks"}, "homepage.comparison.feature8": {"message": "Seamless Integration Between Tools"}, "homepage.comparison.note": {"message": "FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."}, "homepage.caseStudies.title": {"message": "Real-World Success Stories"}, "homepage.caseStudies.description": {"message": "See how organizations and individuals are transforming their work with FunBlocks AI's comprehensive suite of tools"}, "homepage.caseStudies.case1.title": {"message": "Educational Institution Transformation"}, "homepage.caseStudies.case1.description": {"message": "A leading university implemented FunBlocks AI Education Tools (BloomBrain, MarzanoBrain, AIFlow, AI Slides) to enhance teaching materials and develop students' critical thinking skills. The comprehensive suite dramatically improved teaching efficiency and student learning outcomes."}, "homepage.caseStudies.case1.result1": {"message": "65% faster creation of high-quality teaching materials"}, "homepage.caseStudies.case1.result2": {"message": "42% improvement in students' critical thinking abilities"}, "homepage.caseStudies.case1.result3": {"message": "38% increase in creative problem-solving skills"}, "homepage.caseStudies.case1.result4": {"message": "Significant enhancement in visual learning comprehension"}, "homepage.caseStudies.case2.title": {"message": "Corporate Innovation & Productivity"}, "homepage.caseStudies.case2.description": {"message": "A Fortune 500 company integrated FunBlocks AI across their teams to boost creativity and work efficiency. Teams used AIFlow for brainstorming new product features and marketing campaigns, while leveraging the complete workspace for seamless collaboration and execution."}, "homepage.caseStudies.case2.result1": {"message": "3x increase in innovative product feature ideas"}, "homepage.caseStudies.case2.result2": {"message": "58% reduction in meeting time through visual collaboration"}, "homepage.caseStudies.case2.result3": {"message": "45% better cross-team alignment and communication"}, "homepage.caseStudies.case2.result4": {"message": "70% faster creation of professional presentations"}, "homepage.caseStudies.case3.title": {"message": "Personal Growth & Learning"}, "homepage.caseStudies.case3.description": {"message": "Individuals have transformed their lifelong learning and personal development using FunBlocks AI. From creating book mind maps and exploring new topics to generating creative content for social media, users report significant improvements in knowledge retention and creative expression."}, "homepage.caseStudies.case3.result1": {"message": "52% better retention of book concepts with mind maps"}, "homepage.caseStudies.case3.result2": {"message": "4x deeper exploration of interesting topics"}, "homepage.caseStudies.case3.result3": {"message": "68% increase in creative writing output"}, "homepage.caseStudies.case3.result4": {"message": "Significant boost in social media engagement"}, "homepage.caseStudies.case4.title": {"message": "Content Creator Workflow"}, "homepage.caseStudies.case4.description": {"message": "Professional content creators have revolutionized their workflow with FunBlocks AI. Beyond faster content production, they now easily create engaging infographics, insight cards, and visual stories that significantly increase audience engagement and differentiate their content."}, "homepage.caseStudies.case4.result1": {"message": "50% faster content production"}, "homepage.caseStudies.case4.result2": {"message": "75% increase in visual content creation"}, "homepage.caseStudies.case4.result3": {"message": "3x higher engagement with infographics and insight cards"}, "homepage.caseStudies.case4.result4": {"message": "Significant improvement in content uniqueness"}, "homepage.video.title": {"message": "See FunBlocks AIFlow in Action"}, "homepage.video.description": {"message": "Watch how FunBlocks AIFlow transforms the way you think, create, and collaborate"}, "homepage.video.feature1.title": {"message": "Explore"}, "homepage.video.feature1.description": {"message": "See how to explore complex topics visually with AI assistance"}, "homepage.video.feature2.title": {"message": "Think"}, "homepage.video.feature2.description": {"message": "Learn how to enhance your thinking with visual frameworks"}, "homepage.video.feature3.title": {"message": "Create"}, "homepage.video.feature3.description": {"message": "Discover how to transform ideas into professional deliverables"}, "homepage.video.cta": {"message": "Try It Yourself"}, "ai_extensions.meta.title": {"message": "FunBlocks AI Browser Extensions - AI-Powered Web Tools"}, "ai_extensions.meta.description": {"message": "Enhance your browsing with FunBlocks AI Extensions: AI Assistant, Mindmap Generator, and Prompt Optimizer. Boost productivity with AI-powered reading, writing, and thinking tools."}, "ai_extensions.hero.badge": {"message": "BROWSER EXTENSIONS"}, "ai_extensions.hero.title": {"message": "FunBlocks AI Browser Extensions"}, "ai_extensions.hero.subtitle": {"message": "Enhance your browsing experience with AI-powered reading, writing, and thinking tools. Available anytime, anywhere on the web."}, "ai_extensions.hero.explore": {"message": "Explore Extensions"}, "ai_extensions.hero.learn_more": {"message": "Learn More"}, "ai_extensions.hero.stat1": {"message": "Powerful Extensions"}, "ai_extensions.hero.stat2": {"message": "Active Users"}, "ai_extensions.hero.stat3": {"message": "User Rating"}, "ai_extensions.benefits.title": {"message": "Key Benefits"}, "ai_extensions.benefits.description": {"message": "Discover how FunBlocks AI Browser Extensions transform your daily web experience with powerful AI capabilities."}, "ai_extensions.benefits.convenience.title": {"message": "Always Available Convenience"}, "ai_extensions.benefits.convenience.description": {"message": "Access powerful AI tools instantly while browsing any website or working on any task, without switching between applications."}, "ai_extensions.benefits.context.title": {"message": "Automatic Context Capture"}, "ai_extensions.benefits.context.description": {"message": "Automatically capture webpage content as context for AI assistance, eliminating the need for manual copy-pasting."}, "ai_extensions.benefits.efficiency.title": {"message": "Dramatically Improved Efficiency"}, "ai_extensions.benefits.efficiency.description": {"message": "Process web content with AI assistance to boost productivity by 10x in reading, writing, and thinking tasks."}, "ai_extensions.benefits.thinking.title": {"message": "Enhanced Critical Thinking"}, "ai_extensions.benefits.thinking.description": {"message": "Develop stronger analytical skills with AI-powered critical thinking frameworks and structured analysis tools."}, "ai_extensions.unique_value.title": {"message": "Why Choose FunBlocks AI Extensions?"}, "ai_extensions.unique_value.subtitle": {"message": "Beyond task completion - we focus on cultivating your higher-order thinking abilities"}, "ai_extensions.unique_value.philosophy_title": {"message": "AI Enhances Human Intelligence, Not Replaces It"}, "ai_extensions.unique_value.philosophy_description": {"message": "While other extensions focus on quick task completion, FunBlocks AI Extensions prioritize developing your critical thinking and cognitive abilities for long-term growth."}, "ai_extensions.unique_value.comprehensive_title": {"message": "Comprehensive & Intuitive Information Presentation"}, "ai_extensions.unique_value.comprehensive_description": {"message": "Transform complex information into visual mind maps, structured frameworks, and interactive presentations that make understanding effortless and comprehensive."}, "ai_extensions.unique_value.multiperspective_title": {"message": "Multi-Perspective Deep Exploration"}, "ai_extensions.unique_value.multiperspective_description": {"message": "Explore topics from multiple angles using structured thinking frameworks like Six Thinking Hats and SWOT Analysis, preventing information silos and echo chambers."}, "ai_extensions.unique_value.critical_analysis_title": {"message": "Advanced Critical Analysis Capabilities"}, "ai_extensions.unique_value.critical_analysis_description": {"message": "Detect biases, generate counterexamples, and perform comprehensive critical analysis that other extensions simply cannot provide."}, "ai_extensions.unique_value.features_title": {"message": "Exclusive Features You Won't Find Elsewhere"}, "ai_extensions.unique_value.feature1_title": {"message": "Bias Detection & Analysis"}, "ai_extensions.unique_value.feature1_description": {"message": "Automatically identify potential biases in content and AI responses"}, "ai_extensions.unique_value.feature2_title": {"message": "Counterexample Generation"}, "ai_extensions.unique_value.feature2_description": {"message": "Generate opposing viewpoints and counterexamples to strengthen critical thinking"}, "ai_extensions.unique_value.feature3_title": {"message": "Structured Thinking Frameworks"}, "ai_extensions.unique_value.feature3_description": {"message": "Apply proven methodologies like Six Thinking Hats, SWOT, and First Principles"}, "ai_extensions.unique_value.feature4_title": {"message": "Visual Mind Mapping"}, "ai_extensions.unique_value.feature4_description": {"message": "Transform linear text into comprehensive visual knowledge structures"}, "ai_extensions.unique_value.feature5_title": {"message": "Comprehensive Critical Analysis"}, "ai_extensions.unique_value.feature5_description": {"message": "Perform deep analytical thinking that goes beyond surface-level responses"}, "ai_extensions.unique_value.feature6_title": {"message": "Anti-Echo Chamber Technology"}, "ai_extensions.unique_value.feature6_description": {"message": "Actively prevent information silos by presenting diverse perspectives"}, "ai_extensions.unique_value.cta_description": {"message": "Experience the difference that true cognitive enhancement makes. <PERSON><PERSON> FunBlocks AI Extensions for thinking that matters in the AI era."}, "ai_extensions.overview.title": {"message": "Our Browser Extensions"}, "ai_extensions.overview.description": {"message": "Choose the perfect AI extension for your needs. Each extension is designed for specific use cases while maintaining seamless integration with the FunBlocks AI ecosystem."}, "ai_extensions.overview.learn_more": {"message": "Learn More"}, "ai_extensions.overview.install": {"message": "Install"}, "ai_extensions.overview.extension1.title": {"message": "FunBlocks AI Assistant"}, "ai_extensions.overview.extension1.subtitle": {"message": "Complete AI-powered browsing assistant with writing, reading, and thinking tools"}, "ai_extensions.overview.extension1.description": {"message": "The most comprehensive AI browser extension that includes all features from our other extensions plus advanced writing assistance, contextual AI tools, and seamless integration with FunBlocks AIFlow."}, "ai_extensions.overview.extension1.feature1": {"message": "AI Writing Assistant with contextual toolbar"}, "ai_extensions.overview.extension1.feature2": {"message": "AI Reading Assistant with critical thinking frameworks"}, "ai_extensions.overview.extension1.feature3": {"message": "Mind mapping and brainstorming tools"}, "ai_extensions.overview.extension1.feature4": {"message": "Smart widgets for email, video, and more"}, "ai_extensions.overview.extension2.title": {"message": "AI MindMap Generator"}, "ai_extensions.overview.extension2.subtitle": {"message": "Transform web content, PDFs and videos into visual mind maps instantly"}, "ai_extensions.overview.extension2.description": {"message": "Specialized extension focused on helping users read and understand web content and YouTube videos by generating visual mind maps, brainstorming, and critical analysis."}, "ai_extensions.overview.extension2.feature1": {"message": "One-click web page mind mapping"}, "ai_extensions.overview.extension2.feature2": {"message": "YouTube video transcript analysis"}, "ai_extensions.overview.extension2.feature3": {"message": "AI-powered brainstorming and analysis"}, "ai_extensions.overview.extension2.feature4": {"message": "Direct integration with FunBlocks AIFlow"}, "ai_extensions.overview.extension2.feature5": {"message": "PDFs content quick explorer"}, "ai_extensions.overview.extension3.title": {"message": "AI Prompt Optimizer"}, "ai_extensions.overview.extension3.subtitle": {"message": "Enhance AI conversations with prompt optimization and critical thinking"}, "ai_extensions.overview.extension3.description": {"message": "Specialized tool for improving AI interactions across ChatGPT, Claude, Gemini, and other AI platforms with prompt optimization, critical analysis, and thinking enhancement features."}, "ai_extensions.overview.extension3.feature1": {"message": "One-click prompt optimization"}, "ai_extensions.overview.extension3.feature2": {"message": "Critical thinking assistant"}, "ai_extensions.overview.extension3.feature3": {"message": "Related questions and topics generation"}, "ai_extensions.overview.extension3.feature4": {"message": "Multi-platform AI support"}, "ai_extensions.comparison.title": {"message": "Extension Comparison"}, "ai_extensions.comparison.description": {"message": "Understand the differences between our extensions to choose the right one for your needs."}, "ai_extensions.comparison.feature": {"message": "Feature"}, "ai_extensions.comparison.funblocks_ai": {"message": "FunBlocks AI Assistant"}, "ai_extensions.comparison.mindmap": {"message": "AI MindMap Generator"}, "ai_extensions.comparison.prompt_optimizer": {"message": "AI Prompt Optimizer"}, "ai_extensions.comparison.writing_assistant": {"message": "AI Writing Assistant"}, "ai_extensions.comparison.mindmapping": {"message": "Mind Mapping"}, "ai_extensions.comparison.prompt_optimization": {"message": "Prompt Optimization"}, "ai_extensions.comparison.critical_thinking": {"message": "Critical Thinking Tools"}, "ai_extensions.comparison.contextual_tools": {"message": "Contextual AI Tools"}, "ai_extensions.comparison.aiflow_integration": {"message": "AIFlow Integration"}, "ai_extensions.comparison.note": {"message": "The FunBlocks AI Assistant includes all features from the AI MindMap Generator plus additional writing and contextual tools. Choose the specialized extensions if you only need specific functionality."}, "ai_extensions.faq.title": {"message": "Frequently Asked Questions"}, "ai_extensions.faq.q1": {"message": "What is the difference between FunBlocks AI Assistant and AI MindMap Generator?"}, "ai_extensions.faq.a1": {"message": "The FunBlocks AI Assistant is comprehensive and includes all features from the AI MindMap Generator plus additional writing assistance, contextual AI tools, and smart widgets. The AI MindMap Generator is specialized for reading web content and videos, generating mind maps and brainstorming."}, "ai_extensions.faq.q2": {"message": "Which extension should I choose?"}, "ai_extensions.faq.a2": {"message": "Choose FunBlocks AI Assistant for comprehensive AI assistance across all web activities. Choose AI MindMap Generator if you primarily need mind mapping and content analysis. Choose AI Prompt Optimizer if you want to enhance your interactions with AI platforms like ChatGPT and Claude."}, "ai_extensions.faq.q3": {"message": "Are these extensions free to use?"}, "ai_extensions.faq.a3": {"message": "Yes, all extensions offer free usage with daily quotas. New users receive bonus credits, and you can upgrade to FunBlocks AI plans for unlimited usage and additional features."}, "ai_extensions.faq.q4": {"message": "Do I need to install all three extensions?"}, "ai_extensions.faq.a4": {"message": "No, you can choose based on your needs. The FunBlocks AI Assistant provides the most comprehensive experience, while the other two are specialized for specific use cases."}, "ai_extensions.faq.q5": {"message": "How do FunBlocks AI extensions enhance productivity?"}, "ai_extensions.faq.a5": {"message": "Our extensions boost productivity by providing instant AI assistance without switching applications. They automatically capture webpage context, offer contextual AI tools, generate visual content like mind maps and infographics, and provide critical thinking frameworks to analyze information more effectively."}, "ai_extensions.faq.q6": {"message": "Which browsers are supported?"}, "ai_extensions.faq.a6": {"message": "All FunBlocks AI extensions support Google Chrome and Microsoft Edge browsers. We are working on expanding support to Firefox and Safari in the near future."}, "ai_extensions.faq.q7": {"message": "Can I use these extensions with different AI models?"}, "ai_extensions.faq.a7": {"message": "Yes! Our extensions work with multiple AI models including OpenAI GPT, Anthropic Claude, Google Gemini, and more. You can switch between different AI providers based on your preferences and needs."}, "ai_extensions.faq.q8": {"message": "How do the critical thinking features work?"}, "ai_extensions.faq.a8": {"message": "Our extensions include built-in critical thinking frameworks like Six Thinking Hats, SWOT Analysis, First Principles Thinking, and more. These tools help you analyze content from multiple perspectives, identify biases, and make more informed decisions."}, "ai_extensions.faq.q9": {"message": "Is my data secure when using these extensions?"}, "ai_extensions.faq.a9": {"message": "Yes, we prioritize your privacy and security. Our extensions only process data when you explicitly request AI assistance, and we follow strict data protection protocols. Your personal information and browsing data are never stored or shared without your consent."}, "ai_extensions.faq.q10": {"message": "Can I customize the AI assistance features?"}, "ai_extensions.faq.a10": {"message": "Absolutely! Our extensions offer extensive customization options. You can adjust AI model preferences, customize thinking frameworks, set up personalized shortcuts, and configure which features appear in different contexts to match your workflow."}, "ai_extensions.cta.title": {"message": "Ready to Transform Your Browsing Experience?"}, "ai_extensions.cta.description": {"message": "Join thousands of users who have enhanced their productivity with FunBlocks AI Browser Extensions."}, "ai_extensions.cta.primary": {"message": "Get Started Free"}, "ai_extensions.cta.secondary": {"message": "View All Extensions"}, "ai_extensions.showcase.title": {"message": "Choose Your AI Superpower"}, "ai_extensions.showcase.description": {"message": "Our powerful extensions to transform how you browse, learn, and create"}, "ai_extensions.showcase.extension1.title": {"message": "FunBlocks AI Assistant"}, "ai_extensions.showcase.extension1.subtitle": {"message": "Your AI writing & reading companion"}, "ai_extensions.showcase.extension1.description": {"message": "Write better, read smarter, think deeper. AI assistance everywhere you browse."}, "ai_extensions.showcase.extension2.title": {"message": "AI MindMap Generator"}, "ai_extensions.showcase.extension2.subtitle": {"message": "Turn content into visual insights"}, "ai_extensions.showcase.extension2.description": {"message": "Transform any webpage, PDFs or video into interactive mind maps instantly."}, "ai_extensions.showcase.extension3.title": {"message": "AI Prompt Optimizer"}, "ai_extensions.showcase.extension3.subtitle": {"message": "Supercharge your AI chats"}, "ai_extensions.showcase.extension3.description": {"message": "Get better AI responses with optimized prompts and critical thinking tools."}, "ai_extensions.showcase.install_now": {"message": "Install Now"}, "ai_extensions.feature.title": {"message": "Experience the Power of AI Extensions"}, "ai_extensions.feature.description": {"message": "Discover how each extension can transform your digital workflow"}, "ai_extensions.feature.extension1.title": {"message": "FunBlocks AI Assistant"}, "ai_extensions.feature.extension1.tagline": {"message": "Your Ultimate AI Companion"}, "ai_extensions.feature.extension1.description": {"message": "Write, read, and think smarter with AI assistance on every webpage. From emails to research, get contextual help wherever you browse."}, "ai_extensions.feature.extension1.feature1": {"message": "Smart Writing Assistant"}, "ai_extensions.feature.extension1.feature2": {"message": "Intelligent Reading Tools"}, "ai_extensions.feature.extension1.feature3": {"message": "Critical Thinking Framework"}, "ai_extensions.feature.extension2.title": {"message": "AI MindMap Generator"}, "ai_extensions.feature.extension2.tagline": {"message": "Visualize Knowledge Instantly"}, "ai_extensions.feature.extension2.description": {"message": "Transform complex content into clear, interactive mind maps. Perfect for students, researchers, and knowledge workers."}, "ai_extensions.feature.extension2.feature1": {"message": "One-Click Mind Maps"}, "ai_extensions.feature.extension2.feature2": {"message": "YouTube Video Analysis"}, "ai_extensions.feature.extension2.feature3": {"message": "Interactive Brainstorming"}, "ai_extensions.feature.extension3.title": {"message": "AI Prompt Optimizer"}, "ai_extensions.feature.extension3.tagline": {"message": "Master AI Conversations"}, "ai_extensions.feature.extension3.description": {"message": "Get better results from ChatGPT, Claude, and other AI tools. Optimize prompts and enhance your thinking process."}, "ai_extensions.feature.extension3.feature1": {"message": "Smart Prompt Enhancement"}, "ai_extensions.feature.extension3.feature2": {"message": "Critical Analysis Tools"}, "ai_extensions.feature.extension3.feature3": {"message": "Multi-Platform Support"}, "ai_extensions.feature.install_free": {"message": "Install Free"}, "ai101.title": {"message": "AI Era"}, "ai101.subtitle": {"message": "A Special Lecture for University Teachers"}, "ai101.slide1.title": {"message": "A Brief History of AI Development"}, "ai101.slide1.subtitle": {"message": "70 Years Journey from Concept to Reality"}, "ai101.slide2.title": {"message": "Why Now?"}, "ai101.slide2.subtitle": {"message": "Perfect Convergence of Three Key Elements"}, "ai101.slide3.title": {"message": "Concept Clarification"}, "ai101.slide3.subtitle": {"message": "Relationship between LLM, GenAI, and AGI"}, "ai101.slide4.title": {"message": "Characteristics of Generative AI"}, "ai101.slide4.subtitle": {"message": "From Recognition to Creation"}, "ai101.slide5.title": {"message": "Core Learning Technologies"}, "ai101.slide5.subtitle": {"message": "Understanding How AI 'Learns'"}, "ai101.slide6.title": {"message": "Three Key Stages of AI Training"}, "ai101.slide6.subtitle": {"message": "From Raw to Intelligent Transformation"}, "ai101.slide7.title": {"message": "Neural Networks: Mimicking Brain Intelligence"}, "ai101.slide7.subtitle": {"message": "From Biological Inspiration to Artificial Implementation"}, "ai101.slide8.title": {"message": "Mathematical Foundations of AI"}, "ai101.slide8.subtitle": {"message": "Emergence of Intelligence in a Probabilistic World"}, "ai101.slide9.title": {"message": "Key Concept Analysis"}, "ai101.slide9.subtitle": {"message": "Understanding AI's Basic Elements"}, "ai101.slide10.title": {"message": "The Nature of Intelligence: Information Compression?"}, "ai101.slide10.subtitle": {"message": "Extracting Patterns from Data"}, "ai101.slide11.title": {"message": "Scaling Law: The Magic of Scale"}, "ai101.slide11.subtitle": {"message": "Bigger is Better?"}, "ai101.slide12.title": {"message": "Does AI Really 'Understand'?"}, "ai101.slide12.subtitle": {"message": "Statistical Patterns vs True Understanding"}, "ai101.slide13.title": {"message": "Can AI Surpass Humans?"}, "ai101.slide13.subtitle": {"message": "Journey Towards Artificial General Intelligence"}, "ai101.slide14.title": {"message": "AI Threats: <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON>?"}, "ai101.slide14.subtitle": {"message": "Rational View of AI Risks"}, "ai101.slide15.title": {"message": "Survival in the AI Era"}, "ai101.slide15.subtitle": {"message": "Adapt to Change, Embrace the Future"}, "ai101.slide18.title": {"message": "AI Communication Skills"}, "ai101.slide18.subtitle": {"message": "Making AI Your Capable Assistant"}, "ai101.slide19.title": {"message": "Learning and Creation in the Generative Era"}, "ai101.slide19.subtitle": {"message": "From Scarcity to Abundance"}, "ai101.slide20.title": {"message": "New Paradigm of Lifelong Learning"}, "ai101.slide20.subtitle": {"message": "From Staged Education to Continuous Growth"}, "ai101.slide21.title": {"message": "Cultivating AI Literacy"}, "ai101.slide21.subtitle": {"message": "Technology Should Amplify Human Potential, Not Replace Humans"}, "ai101.slide22.title": {"message": "Education-Related AI Products"}, "ai101.slide22.subtitle": {"message": "Making AI Your Educational Assistant"}, "ai101.slide23.title": {"message": "FunBlocks AI"}, "ai101.slide23.subtitle": {"message": "Explore, Think and Create with AI"}, "ai101.slide24.title": {"message": "Why Use AI to Help Innovation and Enhance Thinking?"}, "ai101.slide24.subtitle": {"message": "Breaking Through Human Cognitive Limitations"}, "ai101.slide25.title": {"message": "Breaking Through Linear Thinking Limitations"}, "ai101.slide25.subtitle": {"message": "From Chat Thread to Boundless Canvas"}, "ai101.slide26.title": {"message": "Using AI to Enhance Thinking Ability"}, "ai101.slide26.subtitle": {"message": "Let AI Assist Thinking, Not Replace Thinking"}, "ai101.slide27.title": {"message": "Summary and Outlook"}, "ai101.slide27.subtitle": {"message": "Embracing Work and Lifelong Learning Transformation in the AI Era"}, "ai101.slide28.title": {"message": "AI is Here, Join the Revolution!"}, "ai101.slide28.subtitle": {"message": "Questions & Discussion"}, "ai101.content.aiTraining.pretraining": {"message": "Pre-training: Learning language patterns from massive text data"}, "ai101.content.aiTraining.supervised": {"message": "Supervised Fine-tuning: Learning to follow instructions"}, "ai101.content.aiTraining.reinforcement": {"message": "Reinforcement Learning: Aligning with human preferences"}, "ai101.content.neuralNetwork.biological": {"message": "Biological neurons transmit electrical signals"}, "ai101.content.neuralNetwork.artificial": {"message": "Artificial neurons process numerical values"}, "ai101.content.neuralNetwork.layers": {"message": "Multiple layers enable complex pattern recognition"}, "ai101.content.math.probability": {"message": "Everything is probability - no absolute certainty"}, "ai101.content.math.statistics": {"message": "Learning patterns from data through statistical methods"}, "ai101.content.math.optimization": {"message": "Continuously optimizing to find the best solutions"}, "ai101.content.concepts.parameters": {"message": "Parameters: The 'knowledge' stored in neural networks"}, "ai101.content.concepts.tokens": {"message": "Tokens: Basic units of text processing"}, "ai101.content.concepts.attention": {"message": "Attention: Mechanism for focusing on relevant information"}, "ai101.content.intelligence.compression": {"message": "Intelligence may be the ability to compress information efficiently"}, "ai101.content.intelligence.patterns": {"message": "Finding the simplest rules that explain complex phenomena"}, "ai101.content.scaling.bigger": {"message": "More parameters → Better performance"}, "ai101.content.scaling.data": {"message": "More data → More knowledge"}, "ai101.content.scaling.compute": {"message": "More computation → Better training"}, "ai101.content.understanding.statistical": {"message": "AI excels at statistical pattern matching"}, "ai101.content.understanding.semantic": {"message": "But does it truly understand meaning?"}, "ai101.content.agi.current": {"message": "Current AI: Narrow, specialized capabilities"}, "ai101.content.agi.future": {"message": "AGI Goal: General intelligence across all domains"}, "ai101.content.threats.job": {"message": "Job displacement in certain sectors"}, "ai101.content.threats.misinformation": {"message": "Potential for generating misinformation"}, "ai101.content.threats.bias": {"message": "Amplification of existing biases"}, "ai101.content.survival.adapt": {"message": "Embrace change and continuous learning"}, "ai101.content.survival.collaborate": {"message": "Learn to work with AI as a partner"}, "ai101.content.survival.human": {"message": "Focus on uniquely human capabilities"}, "ai101.slide16.title": {"message": "Essential Skills for the AI Era"}, "ai101.slide16.subtitle": {"message": "Developing human capabilities that complement and enhance AI collaboration"}, "ai101.slide16.criticalThinking.title": {"message": "Critical Thinking"}, "ai101.slide16.criticalThinking.content": {"message": "Ability to analyze, evaluate, and synthesize information"}, "ai101.slide16.creativity.title": {"message": "Creativity"}, "ai101.slide16.creativity.content": {"message": "Generate novel ideas and innovative solutions"}, "ai101.slide16.emotionalIntelligence.title": {"message": "Emotional Intelligence"}, "ai101.slide16.emotionalIntelligence.content": {"message": "Understanding and managing emotions in human interactions"}, "ai101.slide16.aiLiteracy.title": {"message": "AI Literacy"}, "ai101.slide16.aiLiteracy.content": {"message": "Understanding AI capabilities, limitations, and ethical implications"}, "ai101.slide16.systemsThinking.title": {"message": "Collaboration Skills"}, "ai101.slide16.systemsThinking.content": {"message": "The ability to work effectively with both humans and AI systems, leveraging team strengths"}, "ai101.slide16.adaptability.title": {"message": "Adaptive Learning"}, "ai101.slide16.adaptability.content": {"message": "The ability to quickly learn new skills and adapt to changing environments"}, "ai101.slide17.title": {"message": "The Art of AI Collaboration"}, "ai101.slide17.subtitle": {"message": "Short, Medium, and Long-term Strategic Planning"}, "ai101.slide17.shortTerm.title": {"message": "Short-term (1-2 years)"}, "ai101.slide17.shortTerm.item1": {"message": "Learn to use AI tools effectively"}, "ai101.slide17.shortTerm.item2": {"message": "Understand AI capabilities and limitations"}, "ai101.slide17.shortTerm.item3": {"message": "Develop prompt engineering skills"}, "ai101.slide17.mediumTerm.title": {"message": "Medium-term (3-5 years)"}, "ai101.slide17.mediumTerm.item1": {"message": "Master human-AI workflow integration"}, "ai101.slide17.mediumTerm.item2": {"message": "Develop AI-assisted teaching methodologies"}, "ai101.slide17.mediumTerm.item3": {"message": "Build AI literacy curriculum"}, "ai101.slide17.longTerm.title": {"message": "Long-term (5+ years)"}, "ai101.slide17.longTerm.item1": {"message": "Lead AI-enhanced educational transformation"}, "ai101.slide17.longTerm.item2": {"message": "Focus on uniquely human educational values"}, "education.hero.title": {"message": "FunBlocks AI Empowers Education: Ushering in a New Era of Intelligent Learning"}, "education.hero.subtitle": {"message": "All-in-One AI Workspace that Revolutionizes Teaching and Learning"}, "education.hero.description": {"message": "FunBlocks AI deeply integrates artificial intelligence technology with educational theory, providing powerful thinking tools for educators and learners to cultivate core competencies for the future."}, "education.hero.cta.primary": {"message": "Try for Free"}, "education.hero.cta.secondary": {"message": "Watch Demo"}, "education.challenges.title": {"message": "Challenges Facing Today's Education"}, "education.challenges.description": {"message": "Educators and learners face unprecedented challenges in the modern educational environment"}, "education.challenges.workload.title": {"message": "Teacher Workload Overload"}, "education.challenges.workload.description": {"message": "Heavy lesson preparation, grading, and administrative tasks consume significant time, affecting direct student interaction"}, "education.challenges.engagement.title": {"message": "Insufficient Student Engagement"}, "education.challenges.engagement.description": {"message": "Traditional teaching methods struggle to spark student interest, especially with complex or uninteresting subjects"}, "education.challenges.diversity.title": {"message": "Diverse Learning Needs"}, "education.challenges.diversity.description": {"message": "Students have different learning paces, styles, and needs, requiring personalized teaching approaches"}, "education.challenges.skills.title": {"message": "Future Skills Development"}, "education.challenges.skills.description": {"message": "Students need to develop 21st-century core skills such as critical thinking, creativity, and AI literacy"}, "education.solution.title": {"message": "FunBlocks AI: Your All-in-One Educational Co-pilot"}, "education.solution.description": {"message": "FunBlocks AI provides comprehensive and powerful solutions to systematically enhance teaching and learning efficiency and depth"}, "education.solution.workspace.title": {"message": "All-in-One AI Workspace"}, "education.solution.workspace.description": {"message": "Integrates mind mapping, document creation, presentation making, and professional AI tools"}, "education.solution.thinking.title": {"message": "Enhance Human Intelligence"}, "education.solution.thinking.description": {"message": "Your Thinking Matters in the Age of AI - we are committed to being an amplifier of human intelligence"}, "education.solution.visual.title": {"message": "Visual-First Interaction"}, "education.solution.visual.description": {"message": "Beyond text experience, enabling multi-dimensional thinking and exploration through boundless canvas"}, "education.features.title": {"message": "FunBlocks AI: Unleashing Educational Potential"}, "education.features.description": {"message": "FunBlocks AI provides a series of powerful tools that deeply integrate into all aspects of education"}, "education.features.visualization.title": {"message": "Knowledge Visualization"}, "education.features.visualization.subtitle": {"message": "Promoting Deep Understanding"}, "education.features.visualization.mindmap": {"message": "AI Mind Maps: Quickly generate clear and structured mind maps"}, "education.features.visualization.infographic": {"message": "AI Infographics: Transform text into visual summaries"}, "education.features.visualization.benefit": {"message": "Help clarify complex concepts and enhance knowledge memory and retention"}, "education.features.content.title": {"message": "Content Creation"}, "education.features.content.subtitle": {"message": "Streamlining Teaching Preparation"}, "education.features.content.slides": {"message": "AI Presentations: Generate professional teaching slides with one click"}, "education.features.content.docs": {"message": "AI Documents: Intelligent writing assistant helps create teaching materials"}, "education.features.content.benefit": {"message": "Significantly save lesson preparation time and improve teaching material quality"}, "education.features.thinking.title": {"message": "Higher-Order Thinking Development"}, "education.features.thinking.subtitle": {"message": "AI Tools Based on Educational Theory"}, "education.features.thinking.bloom": {"message": "AI BloomBrain: Learning design based on Bloom's Taxonomy"}, "education.features.thinking.critical": {"message": "AI Critical Analysis: Develop logical thinking and analytical skills"}, "education.features.thinking.creative": {"message": "AI Brainstorming: Use classic thinking models to spark creativity"}, "education.features.thinking.benefit": {"message": "Cultivate students' innovation abilities and structured thinking"}, "education.cases.title": {"message": "Real Educational Success Stories"}, "education.cases.description": {"message": "Learn how educational institutions and individuals are transforming teaching and learning through FunBlocks AI"}, "education.cases.university.title": {"message": "University Teaching Transformation"}, "education.cases.university.description": {"message": "A cognitive psychology professor at a university used AIFlow to assist teaching, improving students' understanding of complex concepts by 40%"}, "education.cases.university.result1": {"message": "40% improvement in complex concept understanding"}, "education.cases.university.result2": {"message": "60% reduction in teaching material preparation time"}, "education.cases.university.result3": {"message": "Significant increase in student classroom engagement"}, "education.cases.medical.title": {"message": "Medical Education Breakthrough"}, "education.cases.medical.description": {"message": "Medical students used AI MindLadder to learn complex cardiovascular physiology, significantly improving learning efficiency and exam scores"}, "education.cases.medical.result1": {"message": "30-40% reduction in learning time"}, "education.cases.medical.result2": {"message": "27% improvement in exam scores"}, "education.cases.medical.result3": {"message": "Significant increase in knowledge retention"}, "education.cases.k12.title": {"message": "K-12 Innovative Teaching"}, "education.cases.k12.description": {"message": "K-12 teachers used FunBlocks AI educational tools to create interactive learning materials, significantly improving student understanding and engagement"}, "education.cases.k12.result1": {"message": "50% increase in student engagement"}, "education.cases.k12.result2": {"message": "35% improvement in critical thinking skills"}, "education.cases.k12.result3": {"message": "Enhanced creative problem-solving abilities"}, "education.integration.title": {"message": "Seamless Integration: Fitting into Your Educational Environment"}, "education.integration.description": {"message": "FunBlocks AI is designed with focus on usability, accessibility, security, and compatibility with existing ecosystems"}, "education.integration.accessibility.title": {"message": "User-Friendly and Accessible"}, "education.integration.accessibility.free": {"message": "Free Trial: New users can experience core features for free"}, "education.integration.accessibility.api": {"message": "Bring Your Own API Key: Use your own AI model API to reduce costs"}, "education.integration.accessibility.subscription": {"message": "Flexible Subscription: One subscription, all models"}, "education.integration.security.title": {"message": "Data Privacy and Security"}, "education.integration.security.encryption": {"message": "SSL encryption and HTTPS protocol protect data transmission"}, "education.integration.security.access": {"message": "Strict access control mechanisms"}, "education.integration.security.compliance": {"message": "Follow data protection principles and best practices"}, "education.future.title": {"message": "Planning the Future: FunBlocks AI and Educational Evolution"}, "education.future.description": {"message": "FunBlocks AI is committed to shaping the future of human-AI collaborative education"}, "education.future.collaboration.title": {"message": "Promoting Human-AI Collaboration in Learning"}, "education.future.collaboration.description": {"message": "AI as a partner that enhances human intelligence, not a simple replacement, exploring and creating together"}, "education.future.adaptation.title": {"message": "Helping Students Adapt to an AI-Integrated World"}, "education.future.adaptation.description": {"message": "Cultivating students' AI literacy, critical thinking, creativity, and other 21st-century core skills"}, "education.future.evolution.title": {"message": "Evolution of Educator Roles"}, "education.future.evolution.description": {"message": "Transforming from knowledge transmitters to learning facilitators and AI-assisted experience curators"}, "education.cta.title": {"message": "Partner with FunBlocks AI to Revolutionize Your Teaching and Learning"}, "education.cta.description": {"message": "Choosing FunBlocks AI is not just choosing an advanced technological tool, but embracing a smarter, more efficient, and more human-centered educational future"}, "education.cta.primary": {"message": "Start Free Trial Now"}, "education.cta.secondary": {"message": "Learn More About Product Features"}, "education.cta.features": {"message": "✓ Free trial of all core features\n✓ No credit card required\n✓ Start using immediately"}, "education.faq.title": {"message": "Frequently Asked Questions"}, "education.faq.description": {"message": "Get answers to common questions about FunBlocks AI in education"}, "education.faq.q1.question": {"message": "What is FunBlocks AI and how does it help in education?"}, "education.faq.q1.answer": {"message": "FunBlocks AI is an all-in-one AI workspace that integrates artificial intelligence technology with educational theory. It provides powerful thinking tools including AI mind mapping, presentation generation, critical thinking frameworks, and visual learning tools to help educators create engaging materials and students develop 21st-century skills."}, "education.faq.q2.question": {"message": "How does FunBlocks AI reduce teacher workload?"}, "education.faq.q2.answer": {"message": "FunBlocks AI automates and simplifies many time-consuming tasks such as creating presentations, generating mind maps, designing infographics, and structuring lesson plans based on educational frameworks like Bloom's Taxonomy. This allows teachers to focus more on direct student interaction and creative teaching approaches."}, "education.faq.q3.question": {"message": "What educational frameworks does FunBlocks AI support?"}, "education.faq.q3.answer": {"message": "FunBlocks AI supports various proven educational frameworks including Bloom's Taxonomy (BloomBrain), Marzano's Taxonomy (MarzanoBrain), SOLO Taxonomy (SOLOBrain), and Depth of Knowledge (DOKBrain). These tools help structure learning experiences according to established pedagogical principles."}, "education.faq.q4.question": {"message": "Is FunBlocks AI suitable for all education levels?"}, "education.faq.q4.answer": {"message": "Yes, FunBlocks AI is designed to support education across all levels, from K-12 to higher education and professional development. The tools can be adapted to different complexity levels and learning needs, making them versatile for various educational contexts."}, "education.faq.q5.question": {"message": "How does FunBlocks AI ensure data privacy and security?"}, "education.faq.q5.answer": {"message": "FunBlocks AI prioritizes data security through SSL encryption, HTTPS protocols, strict access control mechanisms, and adherence to data protection principles. The platform also offers a \"Bring Your Own API Key\" option, giving users control over their data and AI model usage."}, "education.faq.q6.question": {"message": "Can I try FunBlocks AI for free?"}, "education.faq.q6.answer": {"message": "Yes, FunBlocks AI offers a free trial that allows new users to experience all core features without requiring a credit card. Additionally, users can bring their own API keys to access premium features at reduced costs, making it accessible for educational institutions with budget constraints."}, "education.aiEra.title": {"message": "AI Era Education: Challenges and Opportunities"}, "education.aiEra.description": {"message": "The AI era brings both unprecedented challenges and transformative opportunities to education"}, "education.aiEra.challenges.subtitle": {"message": "Key Challenges"}, "education.aiEra.workload.title": {"message": "Teaching Load and Efficiency Balance"}, "education.aiEra.workload.description": {"message": "How to reduce teacher workload while maintaining high-quality teaching and personalized attention"}, "education.aiEra.collaboration.title": {"message": "Human-AI Collaboration Model"}, "education.aiEra.collaboration.description": {"message": "Establishing effective human-AI collaborative relationships to leverage respective strengths and maximize educational outcomes"}, "education.aiEra.thinking.title": {"message": "Higher-Order Thinking Development"}, "education.aiEra.thinking.description": {"message": "How to cultivate students' critical thinking, creativity, and complex problem-solving skills in AI-assisted environments"}, "education.aiEra.transformation.title": {"message": "Educational Model Transformation"}, "education.aiEra.transformation.description": {"message": "Adapting to AI era needs by redefining teaching methods, assessment systems, and learning objectives"}, "education.aiEra.opportunities.subtitle": {"message": "Transformative Opportunities"}, "education.aiEra.opportunities.personalized.title": {"message": "Personalized Learning Revolution"}, "education.aiEra.opportunities.personalized.description": {"message": "AI technology makes large-scale personalized education possible, providing customized learning experiences for every student"}, "education.aiEra.opportunities.efficiency.title": {"message": "Teaching Efficiency Enhancement"}, "education.aiEra.opportunities.efficiency.description": {"message": "Automating repetitive tasks allows educators to focus on creative teaching and student interaction"}, "education.aiEra.opportunities.insights.title": {"message": "Data-Driven Insights"}, "education.aiEra.opportunities.insights.description": {"message": "Through learning data analysis, gain deep insights into student needs and optimize teaching strategies"}, "education.collaboration.title": {"message": "Human-AI Collaboration: The Future of Education"}, "education.collaboration.description": {"message": "FunBlocks AI champions a human-centered approach where AI amplifies human intelligence rather than replacing it"}, "education.collaboration.principles.subtitle": {"message": "Core Principles of Human-AI Collaboration"}, "education.collaboration.partnership.title": {"message": "AI as Thinking Partner"}, "education.collaboration.partnership.description": {"message": "AI is not a replacement but an intelligent partner that enhances human cognitive abilities for exploration and creation"}, "education.collaboration.augmentation.title": {"message": "Augmentation Not Replacement"}, "education.collaboration.augmentation.description": {"message": "AI technology amplifies human creativity, critical thinking, and problem-solving capabilities"}, "education.collaboration.balance.title": {"message": "Complementary Human-AI Strengths"}, "education.collaboration.balance.description": {"message": "Leveraging human emotional intelligence and creativity with AI's computational power and data processing advantages"}, "education.solution.subtitle": {"message": "FunBlocks AI: Your All-in-One Educational Co-pilot"}, "education.thinking.title": {"message": "Higher-Order Thinking in the AI Era"}, "education.thinking.description": {"message": "Developing critical thinking, creativity, and complex problem-solving skills that complement AI capabilities"}, "education.thinking.definition.title": {"message": "What is Higher-Order Thinking?"}, "education.thinking.definition.text": {"message": "Higher-order thinking involves complex cognitive processes that go beyond basic recall and comprehension. It includes analysis, synthesis, evaluation, and creation - skills that enable students to think critically, solve complex problems, and generate innovative solutions."}, "education.thinking.analysis.title": {"message": "Analytical Thinking"}, "education.thinking.analysis.description": {"message": "Breaking down complex information to identify patterns, relationships, and causal connections"}, "education.thinking.analysis.tools": {"message": "AI LogicLens, AI Critical Analysis, AI Mind Mapping"}, "education.thinking.synthesis.title": {"message": "Synthetic Thinking"}, "education.thinking.synthesis.description": {"message": "Integrating different information sources to form new understanding and insights"}, "education.thinking.synthesis.tools": {"message": "AIFlow Boundless <PERSON>, AI Brainstorming, AI MindKit"}, "education.thinking.evaluation.title": {"message": "Evaluative Thinking"}, "education.thinking.evaluation.description": {"message": "Judging the value, credibility, and relevance of information to make informed decisions"}, "education.thinking.evaluation.tools": {"message": "AI Reflection Coach, AI Question Craft, AI Critical Analysis"}, "education.thinking.creation.title": {"message": "Creative Thinking"}, "education.thinking.creation.description": {"message": "Generating original ideas, solutions, and innovative outcomes"}, "education.thinking.creation.tools": {"message": "AI Brainstorming, AI Infographics, AI Slide Generation"}, "education.thinking.tools.label": {"message": "FunBlocks AI Tools:"}, "education.features.subtitle": {"message": "Core Features: Unleashing Educational Potential"}, "education.skills.title": {"message": "Essential Skills for the AI Era"}, "education.skills.description": {"message": "Developing human capabilities that complement and enhance AI collaboration"}, "education.skills.critical.title": {"message": "Critical Thinking"}, "education.skills.critical.description": {"message": "The ability to analyze, evaluate, and synthesize information, question assumptions, and identify biases and logical fallacies"}, "education.skills.creative.title": {"message": "Creative Thinking"}, "education.skills.creative.description": {"message": "The ability to generate novel ideas, innovative solutions, and original content"}, "education.skills.emotional.title": {"message": "Emotional Intelligence"}, "education.skills.emotional.description": {"message": "The ability to understand and manage emotions and build effective interpersonal relationships"}, "education.skills.collaboration.title": {"message": "Collaboration Skills"}, "education.skills.collaboration.description": {"message": "The ability to work effectively with both humans and AI systems, leveraging team strengths"}, "education.skills.adaptability.title": {"message": "Adaptive Learning"}, "education.skills.adaptability.description": {"message": "The ability to quickly learn new skills and adapt to changing environments"}, "education.skills.aiLiteracy.title": {"message": "AI Literacy"}, "education.skills.aiLiteracy.description": {"message": "Understanding AI capabilities, limitations, and ethical implications, and effectively using AI tools"}, "education.skills.category.cognitive": {"message": "Cognitive Skills"}, "education.skills.category.social": {"message": "Social Skills"}, "education.skills.category.meta": {"message": "Meta Skills"}, "education.skills.help.title": {"message": "How FunBlocks AI Develops These Skills"}, "education.skills.help.practice.title": {"message": "Structured Practice"}, "education.skills.help.practice.description": {"message": "AI tools like BloomBrain and Critical Analysis provide structured frameworks for developing thinking skills"}, "education.skills.help.collaboration.title": {"message": "AI Collaboration Experience"}, "education.skills.help.collaboration.description": {"message": "Students learn to work with AI as a thinking partner, developing essential collaboration skills"}, "education.skills.help.creativity.title": {"message": "Creative Expression"}, "education.skills.help.creativity.description": {"message": "Visual tools and brainstorming features encourage creative thinking and innovative problem-solving"}, "education.future.subtitle": {"message": "The Future of Education"}, "education.future.hero.title": {"message": "FunBlocks AI: Shaping the Future of Education"}, "education.future.hero.subtitle": {"message": "Human-AI Collaboration for Next-Generation Learning"}, "education.future.hero.description": {"message": "In the AI era, education faces unprecedented transformation. FunBlocks AI empowers educators and learners with advanced thinking tools to develop essential skills for the future, fostering human-AI collaboration that enhances rather than replaces human intelligence."}, "education.future.hero.cta.primary": {"message": "Explore Solutions"}, "education.future.hero.cta.secondary": {"message": "See AI in Action"}, "education.future.impact.title": {"message": "The AI Revolution: Transforming Our World"}, "education.future.impact.description": {"message": "Artificial Intelligence is reshaping every aspect of human life, creating both unprecedented opportunities and fundamental challenges that require new approaches to education and learning."}, "education.future.impact.society.title": {"message": "Societal Transformation"}, "education.future.impact.society.description": {"message": "AI is redefining work, economy, and social structures, requiring us to rethink the goals and methods of education."}, "education.future.impact.individual.title": {"message": "Individual Development"}, "education.future.impact.individual.description": {"message": "Every person needs to develop new skills and mindsets to thrive in an AI-enhanced world."}, "education.future.impact.workplace.title": {"message": "Workplace Evolution"}, "education.future.impact.workplace.description": {"message": "Future work will increasingly rely on human-AI collaboration, requiring new skill sets and ways of working."}, "education.future.impact.education.title": {"message": "Educational System Restructuring"}, "education.future.impact.education.description": {"message": "Traditional educational models must adapt to AI era demands, cultivating students' critical thinking and innovation capabilities."}, "education.future.impact.insight.title": {"message": "The Question Isn't Whether AI Will Change Education"}, "education.future.impact.insight.text": {"message": "The question is: How can we prepare learners to thrive in an AI-augmented world while preserving and enhancing uniquely human capabilities?"}, "education.future.collaboration.main.title": {"message": "Human-AI Collaboration: The Heart of Future Education"}, "education.future.collaboration.main.description": {"message": "The future of education lies not in replacing humans with AI, but in creating powerful partnerships where AI amplifies human intelligence, creativity, and wisdom."}, "education.future.collaboration.philosophy.title": {"message": "Our Philosophy: Human-Centered AI"}, "education.future.collaboration.philosophy.text": {"message": "AI should enhance human capabilities, not replace them. In education, this means creating tools that help teachers teach better and students learn deeper, while preserving the irreplaceable human elements of empathy, creativity, and critical thinking."}, "education.future.collaboration.partnership.title": {"message": "AI as Thinking Partner"}, "education.future.collaboration.partnership.description": {"message": "AI is not a replacement but an intelligent partner that enhances human cognitive abilities for exploration and creation."}, "education.future.collaboration.augmentation.title": {"message": "Augmentation Not Replacement"}, "education.future.collaboration.augmentation.description": {"message": "AI technology amplifies human creativity, critical thinking, and problem-solving capabilities rather than replacing them."}, "education.future.collaboration.balance.title": {"message": "Complementary Human-AI Strengths"}, "education.future.collaboration.balance.description": {"message": "Leveraging human emotional intelligence and creativity with AI's computational power and data processing advantages."}, "education.future.collaboration.benefits.title": {"message": "Why Human-AI Collaboration Works"}, "education.future.collaboration.benefits.efficiency.title": {"message": "Enhanced Efficiency"}, "education.future.collaboration.benefits.efficiency.description": {"message": "AI handles repetitive tasks, allowing humans to focus on creative and strategic work."}, "education.future.collaboration.benefits.creativity.title": {"message": "Sparked Creativity"}, "education.future.collaboration.benefits.creativity.description": {"message": "AI provides new perspectives and possibilities, inspiring human innovative thinking."}, "education.future.collaboration.benefits.growth.title": {"message": "Promoted Growth"}, "education.future.collaboration.benefits.growth.description": {"message": "Human-AI collaboration creates opportunities for continuous learning and skill development."}, "education.future.skills.title": {"message": "Essential Human Qualities for the AI Era"}, "education.future.skills.description": {"message": "As AI transforms our world, certain uniquely human capabilities become more valuable than ever. These skills complement AI's strengths and ensure humans remain at the center of meaningful work and learning."}, "education.future.skills.cognitive.category": {"message": "Cognitive Skills"}, "education.future.skills.social.category": {"message": "Social Skills"}, "education.future.skills.meta.category": {"message": "Meta Skills"}, "education.future.skills.critical.title": {"message": "Critical Thinking"}, "education.future.skills.critical.description": {"message": "The ability to analyze, evaluate, and synthesize information, question assumptions, and identify biases and logical fallacies."}, "education.future.skills.creative.title": {"message": "Creative Thinking"}, "education.future.skills.creative.description": {"message": "The ability to generate novel ideas, innovative solutions, and original content."}, "education.future.skills.analytical.title": {"message": "Analytical Thinking"}, "education.future.skills.analytical.description": {"message": "The ability to break down complex problems, identify patterns and relationships, and make data-driven decisions."}, "education.future.skills.emotional.title": {"message": "Emotional Intelligence"}, "education.future.skills.emotional.description": {"message": "The ability to understand and manage emotions and build effective interpersonal relationships."}, "education.future.skills.collaboration.title": {"message": "Collaboration Skills"}, "education.future.skills.collaboration.description": {"message": "The ability to work effectively with both humans and AI systems, leveraging team strengths."}, "education.future.skills.communication.title": {"message": "Communication Skills"}, "education.future.skills.communication.description": {"message": "The ability to express ideas clearly, listen effectively, and communicate across cultures and media."}, "education.future.skills.adaptability.title": {"message": "Adaptive Learning"}, "education.future.skills.adaptability.description": {"message": "The ability to quickly learn new skills and adapt to changing environments."}, "education.future.skills.aiLiteracy.title": {"message": "AI Literacy"}, "education.future.skills.aiLiteracy.description": {"message": "Understanding AI capabilities, limitations, and ethical implications, and effectively using AI tools."}, "education.future.skills.learning.title": {"message": "Lifelong Learning"}, "education.future.skills.learning.description": {"message": "The ability to continuously learn and develop oneself, maintaining curiosity and a growth mindset."}, "education.future.skills.insight.title": {"message": "The Human Advantage in an AI World"}, "education.future.skills.insight.text": {"message": "While AI excels at processing information and pattern recognition, humans bring irreplaceable qualities: empathy, ethical reasoning, creative problem-solving, and the ability to find meaning and purpose. The future belongs to those who can effectively combine these human strengths with AI capabilities."}, "education.future.thinking.title": {"message": "Higher-Order Thinking: The Foundation of Future Learning"}, "education.future.thinking.description": {"message": "In an age where AI can process information instantly, the ability to think critically, creatively, and analytically becomes humanity's greatest asset. Higher-order thinking skills are what separate human intelligence from artificial intelligence."}, "education.future.thinking.definition.title": {"message": "What Makes Thinking \"Higher-Order\"?"}, "education.future.thinking.definition.text": {"message": "Higher-order thinking goes beyond memorization and basic comprehension. It involves analyzing complex information, synthesizing ideas from multiple sources, evaluating evidence and arguments, and creating original solutions. These cognitive processes are essential for navigating an AI-enhanced world."}, "education.future.thinking.framework.title": {"message": "The Four Pillars of Higher-Order Thinking"}, "education.future.thinking.analysis.title": {"message": "Analytical Thinking"}, "education.future.thinking.analysis.description": {"message": "Breaking down complex information to identify patterns, relationships, and causal connections."}, "education.future.thinking.analysis.example": {"message": "Analyzing the methodology and validity of conclusions in a research paper."}, "education.future.thinking.synthesis.title": {"message": "Synthetic Thinking"}, "education.future.thinking.synthesis.description": {"message": "Integrating different information sources to form new understanding and insights."}, "education.future.thinking.synthesis.example": {"message": "Combining historical, economic, and sociological perspectives to understand current events."}, "education.future.thinking.evaluation.title": {"message": "Evaluative Thinking"}, "education.future.thinking.evaluation.description": {"message": "Judging the value, credibility, and relevance of information to make informed decisions."}, "education.future.thinking.evaluation.example": {"message": "Evaluating the credibility and bias of different news sources."}, "education.future.thinking.creation.title": {"message": "Creative Thinking"}, "education.future.thinking.creation.description": {"message": "Generating original ideas, solutions, and innovative outcomes."}, "education.future.thinking.creation.example": {"message": "Designing innovative solutions to environmental problems."}, "education.future.thinking.example.label": {"message": "Example:"}, "education.future.thinking.importance.title": {"message": "Why Higher-Order Thinking Matters More Than Ever"}, "education.future.thinking.importance.innovation.title": {"message": "Drives Innovation"}, "education.future.thinking.importance.innovation.description": {"message": "Creative and critical thinking leads to breakthrough solutions that AI alone cannot generate."}, "education.future.thinking.importance.resilience.title": {"message": "Builds Resilience"}, "education.future.thinking.importance.resilience.description": {"message": "Analytical skills help navigate uncertainty and adapt to rapid technological change."}, "education.future.thinking.importance.purpose.title": {"message": "Provides Purpose"}, "education.future.thinking.importance.purpose.description": {"message": "Evaluative thinking helps determine what matters most and guides ethical decision-making."}, "education.future.solutions.title": {"message": "FunBlocks AI: Empowering Higher-Order Thinking"}, "education.future.solutions.description": {"message": "Our comprehensive AI-powered platform is specifically designed to enhance human thinking capabilities, not replace them. Every tool is built with educational theory and cognitive science principles at its core."}, "education.future.solutions.aiflow.title": {"message": "AIFlow Mind Mapping"}, "education.future.solutions.aiflow.description": {"message": "Visual thinking tools that help break down complex concepts, build knowledge connections, and promote deep understanding."}, "education.future.solutions.aiflow.feature1": {"message": "AI-assisted concept mapping and relationship analysis"}, "education.future.solutions.aiflow.feature2": {"message": "Structured thinking guidance based on educational frameworks"}, "education.future.solutions.aiflow.feature3": {"message": "Collaborative learning and knowledge sharing platform"}, "education.future.solutions.workspace.title": {"message": "All-in-One AI Workspace"}, "education.future.solutions.workspace.description": {"message": "Complete learning and teaching environment integrating documents, presentations, and infographic creation."}, "education.future.solutions.workspace.feature1": {"message": "AI-powered content creation and editing tools"}, "education.future.solutions.workspace.feature2": {"message": "Multimedia learning resource integration platform"}, "education.future.solutions.workspace.feature3": {"message": "Personalized learning paths and progress tracking"}, "education.future.solutions.tools.title": {"message": "Professional Educational AI Tools"}, "education.future.solutions.tools.description": {"message": "Professional AI tool suite based on educational theories like Bloom's Taxonomy."}, "education.future.solutions.tools.feature1": {"message": "BloomBrain, MarzanoBrain and other cognitive framework tools"}, "education.future.solutions.tools.feature2": {"message": "Critical thinking and creative thinking training tools"}, "education.future.solutions.tools.feature3": {"message": "Assessment and reflection support tools"}, "education.future.solutions.explore": {"message": "Explore →"}, "education.future.solutions.benefits.title": {"message": "Proven Impact on Learning Outcomes"}, "education.future.solutions.benefits.efficiency.title": {"message": "Teaching Efficiency Boost"}, "education.future.solutions.benefits.efficiency.description": {"message": "Reduce lesson preparation time while improving teaching quality"}, "education.future.solutions.benefits.engagement.title": {"message": "Learning Engagement Increase"}, "education.future.solutions.benefits.engagement.description": {"message": "Improve student engagement through visual and interactive learning"}, "education.future.solutions.benefits.thinking.title": {"message": "Thinking Skills Enhancement"}, "education.future.solutions.benefits.thinking.description": {"message": "Significantly improve critical thinking and problem-solving abilities"}, "education.future.solutions.howItWorks.title": {"message": "How FunBlocks AI Enhances Higher-Order Thinking"}, "education.future.solutions.workflow.step1.title": {"message": "Structured Frameworks"}, "education.future.solutions.workflow.step1.description": {"message": "Built-in educational frameworks like Bloom's Taxonomy guide thinking processes"}, "education.future.solutions.workflow.step2.title": {"message": "AI-Assisted Exploration"}, "education.future.solutions.workflow.step2.description": {"message": "AI helps break down complex topics and suggests new perspectives"}, "education.future.solutions.workflow.step3.title": {"message": "Visual Synthesis"}, "education.future.solutions.workflow.step3.description": {"message": "Transform ideas into visual formats that enhance understanding and retention"}, "education.future.solutions.workflow.step4.title": {"message": "Reflective Practice"}, "education.future.solutions.workflow.step4.description": {"message": "Built-in reflection tools help consolidate learning and develop metacognition"}, "education.future.transformation.title": {"message": "The Great Educational Transformation"}, "education.future.transformation.description": {"message": "AI is not just changing how we teach and learn—it's fundamentally transforming what education means. This transformation touches every aspect of the educational ecosystem."}, "education.future.transformation.curriculum.title": {"message": "Curriculum Restructuring"}, "education.future.transformation.curriculum.description": {"message": "Shifting from knowledge transmission to competency development, emphasizing interdisciplinary learning and practical application."}, "education.future.transformation.curriculum.change1": {"message": "Competency-based learning replacing knowledge memorization"}, "education.future.transformation.curriculum.change2": {"message": "Interdisciplinary integration and project-based learning"}, "education.future.transformation.curriculum.change3": {"message": "AI literacy becoming core curriculum content"}, "education.future.transformation.pedagogy.title": {"message": "Pedagogical Innovation"}, "education.future.transformation.pedagogy.description": {"message": "Moving from traditional lecturing to guided learning, emphasizing student active participation and critical thinking."}, "education.future.transformation.pedagogy.change1": {"message": "Shift from teacher-centered to student-centered"}, "education.future.transformation.pedagogy.change2": {"message": "Personalized and adaptive learning paths"}, "education.future.transformation.pedagogy.change3": {"message": "Human-AI collaborative teaching models"}, "education.future.transformation.assessment.title": {"message": "Assessment Revolution"}, "education.future.transformation.assessment.description": {"message": "Moving from standardized testing to diversified, process-oriented, and authentic assessment."}, "education.future.transformation.assessment.change1": {"message": "Continuous and formative assessment"}, "education.future.transformation.assessment.change2": {"message": "Project and portfolio assessment"}, "education.future.transformation.assessment.change3": {"message": "AI-assisted personalized feedback"}, "education.future.transformation.roles.title": {"message": "Role Redefinition"}, "education.future.transformation.roles.description": {"message": "Both teacher and student roles are undergoing fundamental changes, adapting to new AI era demands."}, "education.future.transformation.roles.change1": {"message": "Teachers become learning facilitators and mentors"}, "education.future.transformation.roles.change2": {"message": "Students become active knowledge constructors"}, "education.future.transformation.roles.change3": {"message": "AI becomes intelligent learning partner"}, "education.future.transformation.vision.title": {"message": "Our Vision for the Future of Education"}, "education.future.transformation.vision.text": {"message": "We envision an educational future where AI amplifies human potential, where every learner can access personalized, engaging, and meaningful learning experiences, and where educators are empowered to focus on what they do best: inspiring, mentoring, and nurturing the next generation of thinkers and innovators."}, "education.future.cta.title": {"message": "Ready to Shape the Future of Education?"}, "education.future.cta.description": {"message": "Join thousands of educators and learners who are already using FunBlocks AI to enhance their thinking capabilities and prepare for the future. Start your journey toward more effective, engaging, and meaningful education today."}, "education.future.cta.primary": {"message": "FunBlocks AIFlow"}, "education.future.cta.secondary": {"message": "AI Tools"}, "education.future.cta.feature1": {"message": "Free access to all core features"}, "education.future.cta.feature2": {"message": "No credit card required"}, "education.future.cta.feature3": {"message": "Start using immediately"}, "education.future.thinking.bloom.title": {"message": "<PERSON>'s Taxonomy Pyramid"}, "education.future.thinking.bloom.create": {"message": "CREATE"}, "education.future.thinking.bloom.create.desc": {"message": "Design, Build, Plan"}, "education.future.thinking.bloom.evaluate": {"message": "EVALUATE"}, "education.future.thinking.bloom.evaluate.desc": {"message": "Judge, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "education.future.thinking.bloom.analyze": {"message": "ANALYZE"}, "education.future.thinking.bloom.analyze.desc": {"message": "Compare, Contrast, Examine"}, "education.future.thinking.bloom.apply": {"message": "APPLY"}, "education.future.thinking.bloom.apply.desc": {"message": "Use, Execute, Implement"}, "education.future.thinking.bloom.understand": {"message": "UNDERSTAND"}, "education.future.thinking.bloom.understand.desc": {"message": "Explain, <PERSON><PERSON><PERSON>, Summarize"}, "education.future.thinking.bloom.remember": {"message": "REMEMBER"}, "education.future.thinking.bloom.remember.desc": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>gnize, <PERSON>"}, "education.future.thinking.bloom.subtitle": {"message": "Cognitive complexity increases from bottom to top"}, "education.solutions.title": {"message": "Three Pillars of AI-Enhanced Education"}, "education.solutions.description": {"message": "FunBlocks AI transforms traditional education through three interconnected approaches: empowering educators with intelligent tools, developing students' critical thinking abilities, and creating collaborative learning environments where humans and AI work together seamlessly."}, "education.solutions.teachers.title": {"message": "Empower Educators with Intelligent Teaching Tools"}, "education.solutions.teachers.description": {"message": "Transform your teaching workflow with AI-powered tools grounded in educational research. From automated lesson planning to interactive courseware generation, our platform helps educators focus on what matters most: inspiring and guiding students."}, "education.solutions.teachers.feature1": {"message": "AI-powered lesson planning and courseware generation"}, "education.solutions.teachers.feature2": {"message": "Interactive teaching tools based on educational frameworks"}, "education.solutions.teachers.feature3": {"message": "Real-time student engagement analytics and feedback"}, "education.solutions.teachers.feature4": {"message": "Personalized learning path recommendations"}, "education.solutions.students.title": {"message": "Cultivate Critical Thinking and Creative Problem-Solving"}, "education.solutions.students.description": {"message": "Guide students beyond memorization to deep understanding. Our visual thinking tools and cognitive frameworks help learners analyze complex concepts, explore multiple perspectives, and develop the analytical skills essential for the AI era."}, "education.solutions.students.feature1": {"message": "Visual thinking tools for complex concept analysis"}, "education.solutions.students.feature2": {"message": "AI-guided critical and creative thinking exercises"}, "education.solutions.students.feature3": {"message": "Structured problem-solving frameworks"}, "education.solutions.students.feature4": {"message": "Collaborative knowledge construction tools"}, "education.solutions.collaboration.title": {"message": "Foster Human-AI Partnership in Learning"}, "education.solutions.collaboration.description": {"message": "Create dynamic learning environments where students and AI work as partners. Our collaborative platform encourages active exploration, personalized knowledge building, and inquiry-driven discovery that prepares learners for a future of human-AI cooperation."}, "education.solutions.collaboration.feature1": {"message": "AI-human partnership in knowledge creation"}, "education.solutions.collaboration.feature2": {"message": "Inquiry-based learning methodologies"}, "education.solutions.collaboration.feature3": {"message": "Personalized learning environment construction"}, "education.solutions.collaboration.feature4": {"message": "Active learning engagement strategies"}, "education.solutions.highlight.title": {"message": "Where Educational Science Meets AI Innovation"}, "education.solutions.highlight.text": {"message": "We believe technology should amplify human potential, not replace it. That's why every FunBlocks AI feature is built on decades of educational research—from Bloom's Taxonomy to cognitive science—ensuring our AI tools enhance learning while preserving the irreplaceable value of human creativity and critical thinking."}, "education.benefits.title": {"message": "Transformative Benefits for Every User"}, "education.benefits.description": {"message": "FunBlocks AI delivers measurable improvements for both learners and educators, enhancing the educational experience through intelligent technology that amplifies human potential."}, "education.benefits.learners.title": {"message": "Benefits for Learners"}, "education.benefits.learners.critical.title": {"message": "Enhanced Critical Thinking Skills"}, "education.benefits.learners.critical.description": {"message": "Develop core skills in analysis, evaluation, and reasoning through structured thinking frameworks and AI guidance."}, "education.benefits.learners.thinking.title": {"message": "Higher-Order Thinking Development"}, "education.benefits.learners.thinking.description": {"message": "Progress from memorization and comprehension to higher cognitive levels of analysis, synthesis, evaluation, and creation."}, "education.benefits.learners.creativity.title": {"message": "Creative Problem-Solving"}, "education.benefits.learners.creativity.description": {"message": "Explore innovative solutions and diverse perspectives using AI brainstorming and visual thinking tools."}, "education.benefits.learners.analysis.title": {"message": "Improved Information Analysis"}, "education.benefits.learners.analysis.description": {"message": "Learn to effectively process complex information, identify patterns, and synthesize insights from multiple sources."}, "education.benefits.learners.collaboration.title": {"message": "Human-AI Collaboration Skills"}, "education.benefits.learners.collaboration.description": {"message": "Develop the ability to work effectively with AI tools, preparing for future work environments."}, "education.benefits.learners.growth.title": {"message": "Personalized Learning Growth"}, "education.benefits.learners.growth.description": {"message": "Receive customized learning experiences and feedback tailored to individual learning styles and pace."}, "education.benefits.educators.title": {"message": "Benefits for Educators"}, "education.benefits.educators.efficiency.title": {"message": "Significantly Improved Teaching Efficiency"}, "education.benefits.educators.efficiency.description": {"message": "Automate lesson planning, content generation, and assessment tasks to save time for student interaction."}, "education.benefits.educators.personalization.title": {"message": "Personalized Teaching Delivery"}, "education.benefits.educators.personalization.description": {"message": "Create customized teaching materials and learning paths for students with different learning needs."}, "education.benefits.educators.insights.title": {"message": "Deep Teaching Insights"}, "education.benefits.educators.insights.description": {"message": "Understand student learning progress through data analysis and adjust teaching strategies accordingly."}, "education.benefits.educators.tools.title": {"message": "Rich Teaching Toolkit"}, "education.benefits.educators.tools.description": {"message": "Access diverse AI-powered teaching tools to create more engaging learning experiences."}, "education.benefits.educators.innovation.title": {"message": "Teaching Innovation"}, "education.benefits.educators.innovation.description": {"message": "Explore new teaching methods and technologies to maintain competitive advantage at the educational forefront."}, "education.benefits.educators.development.title": {"message": "Professional Development Support"}, "education.benefits.educators.development.description": {"message": "Continuously improve digital literacy and teaching skills through the use of AI tools."}, "education.benefits.highlight.title": {"message": "Proven Results Across Educational Settings"}, "education.benefits.highlight.text": {"message": "From K-12 classrooms to higher education institutions, FunBlocks AI has demonstrated consistent improvements in learning outcomes, teaching efficiency, and student engagement. Our evidence-based approach ensures that every feature contributes to meaningful educational transformation."}, "education.products.title": {"message": "Complete AI Education Solutions for the Future"}, "education.products.description": {"message": "Addressing the educational transformation and AI literacy challenges of our time, FunBlocks AI provides a comprehensive suite of products that work together to create a complete learning ecosystem for the AI era."}, "education.products.aiflow.title": {"message": "AIFlow Mind Mapping"}, "education.products.aiflow.description": {"message": "Visual thinking platform that transforms complex concepts into interactive mind maps, supporting deep analysis and creative exploration of any topic."}, "education.products.aiflow.feature1": {"message": "AI-powered concept mapping and relationship analysis"}, "education.products.aiflow.feature2": {"message": "Critical thinking frameworks and guided exploration"}, "education.products.aiflow.feature3": {"message": "Collaborative knowledge construction and sharing"}, "education.products.workspace.title": {"message": "All-in-One AI Workspace"}, "education.products.workspace.description": {"message": "Comprehensive learning environment integrating writing, presentations, infographics, and collaborative tools for complete educational content creation."}, "education.products.workspace.feature1": {"message": "AI-enhanced writing and content creation tools"}, "education.products.workspace.feature2": {"message": "Integrated multimedia learning resource platform"}, "education.products.workspace.feature3": {"message": "Team collaboration and project management features"}, "education.products.aitools.title": {"message": "Specialized AI Education Tools"}, "education.products.aitools.description": {"message": "Professional AI tool suite based on educational theories like Bloom's Taxonomy, designed specifically for developing higher-order thinking skills."}, "education.products.aitools.feature1": {"message": "BloomBrain, MarzanoBrain and cognitive framework tools"}, "education.products.aitools.feature2": {"message": "Critical thinking and creative problem-solving exercises"}, "education.products.aitools.feature3": {"message": "Assessment and reflection support systems"}, "education.products.explore": {"message": "Explore Product →"}, "education.products.integration.title": {"message": "How Our Solutions Work Together"}, "education.products.integration.step1.title": {"message": "Specialized AI Tools"}, "education.products.integration.step1.description": {"message": "Use BloomBrain, MarzanoBrain, and other cognitive framework tools for targeted skill development"}, "education.products.integration.step2.title": {"message": "Visual Thinking"}, "education.products.integration.step2.description": {"message": "Transform insights into mind maps and visual knowledge structures with AIFlow"}, "education.products.integration.step3.title": {"message": "Complete Workspace"}, "education.products.integration.step3.description": {"message": "Create comprehensive learning materials and collaborate in the all-in-one workspace"}, "education.products.benefits.title": {"message": "Why Choose FunBlocks AI Education Solutions"}, "education.products.benefits.comprehensive.title": {"message": "Comprehensive Coverage"}, "education.products.benefits.comprehensive.description": {"message": "From individual thinking tools to complete learning environments, we cover every aspect of AI-enhanced education"}, "education.products.benefits.research.title": {"message": "Research-Based"}, "education.products.benefits.research.description": {"message": "Every feature is grounded in educational research and cognitive science principles"}, "education.products.benefits.future.title": {"message": "Future-Ready"}, "education.products.benefits.future.description": {"message": "Prepare students and educators for the AI era with cutting-edge tools and methodologies"}, "ai101.educationalTransformation.title": {"message": "Broader Educational Transformation in the AI Era"}, "ai101.educationalTransformation.subtitle": {"message": "Systematic Changes Across Curriculum, Pedagogy, Roles, and Assessment"}, "ai101.educationalTransformation.curriculum.title": {"message": "Curriculum & Pedagogy Redefinition"}, "ai101.educationalTransformation.curriculum.item1": {"message": "Shift to competency-based and interdisciplinary learning"}, "ai101.educationalTransformation.curriculum.item2": {"message": "Emphasis on \"irreplaceable human skills\""}, "ai101.educationalTransformation.curriculum.item3": {"message": "Integration of AI literacy as core curriculum"}, "ai101.educationalTransformation.curriculum.item4": {"message": "Personalized and adaptive coursework"}, "ai101.educationalTransformation.roles.title": {"message": "Evolution of Educator & Learner Roles"}, "ai101.educationalTransformation.roles.item1": {"message": "Educators as \"learning architects\" and guides"}, "ai101.educationalTransformation.roles.item2": {"message": "Learners as active co-creators and critical consumers"}, "ai101.educationalTransformation.roles.item3": {"message": "New skills required: AI literacy, data analysis, ethical guidance"}, "ai101.educationalTransformation.assessment.title": {"message": "New Pathways in Assessment Methods"}, "ai101.educationalTransformation.assessment.item1": {"message": "AI-driven formative and summative assessment"}, "ai101.educationalTransformation.assessment.item2": {"message": "Personalized and adaptive evaluation"}, "ai101.educationalTransformation.assessment.item3": {"message": "Focus on authenticity, process, and continuous feedback"}, "ai101.educationalTransformation.assessment.item4": {"message": "Challenge to traditional assessment validity"}, "ai101.educationalTransformation.humanCentered.title": {"message": "Human-Centered AI in Education"}, "ai101.educationalTransformation.humanCentered.content": {"message": "Systemic Educational Paradigm Shift:\n\nCurriculum → Competency-based, AI literacy integrated\nRoles → Educators as architects, learners as co-creators\nAssessment → Authentic, continuous, human-AI collaborative\nGoal → Human flourishing, agency, and ethical development in AI world"}, "ai101.emergingConcepts.title": {"message": "Dawn of Emerging Educational Concepts"}, "ai101.emergingConcepts.subtitle": {"message": "New Paradigms for Human-AI Symbiosis in Learning"}, "ai101.emergingConcepts.colearning.title": {"message": "Human-AI Co-learning"}, "ai101.emergingConcepts.colearning.description": {"message": "Humans and AI systems learn together, adapt mutually, and co-evolve through collaboration"}, "ai101.emergingConcepts.colearning.item1": {"message": "Shared mental models and common ground"}, "ai101.emergingConcepts.colearning.item2": {"message": "AI as team partner, not just tool"}, "ai101.emergingConcepts.colearning.item3": {"message": "Mutual learning and adaptation through interaction"}, "ai101.emergingConcepts.hcaif.title": {"message": "Human-Centric AI-First Education (HCAIF)"}, "ai101.emergingConcepts.hcaif.description": {"message": "Prioritizes human values, ethical application, and personalized feedback while leveraging AI capabilities"}, "ai101.emergingConcepts.hcaif.item1": {"message": "AI enhances rather than replaces human capabilities"}, "ai101.emergingConcepts.hcaif.item2": {"message": "Integration of technology, UX, and ethical considerations"}, "ai101.emergingConcepts.hcaif.item3": {"message": "Emphasis on attribution and reflection in AI use"}, "ai101.emergingConcepts.irreplaceable.title": {"message": "\"Machine-Irreplaceable Skills\" Philosophy"}, "ai101.emergingConcepts.irreplaceable.description": {"message": "Focus on developing uniquely human capabilities that AI cannot replicate"}, "ai101.emergingConcepts.irreplaceable.item1": {"message": "Critical thinking, creativity, emotional intelligence"}, "ai101.emergingConcepts.irreplaceable.item2": {"message": "Ethical judgment and complex collaboration"}, "ai101.emergingConcepts.irreplaceable.item3": {"message": "Human-centered innovation and value-driven outcomes"}, "ai101.emergingConcepts.epistemological.title": {"message": "Epistemological Knowledge & Adaptability"}, "ai101.emergingConcepts.epistemological.description": {"message": "Understanding how knowledge is constructed, validated, and applied in a rapidly changing world"}, "ai101.emergingConcepts.epistemological.item1": {"message": "Meta-cognitive skills and learning strategies"}, "ai101.emergingConcepts.epistemological.item2": {"message": "Adaptability and lifelong learning capabilities"}, "ai101.emergingConcepts.epistemological.item3": {"message": "Integration of programming, data science, complex systems"}, "ai101.aiParadigms.title": {"message": "Core Principles of Emerging AI-Driven Educational Paradigms"}, "ai101.aiParadigms.subtitle": {"message": "Comparative Analysis of New Educational Approaches"}, "ai101.aiParadigms.table.paradigm": {"message": "Emerging Paradigm"}, "ai101.aiParadigms.table.principles": {"message": "Core Principles"}, "ai101.aiParadigms.table.humanRole": {"message": "Human Learner Role"}, "ai101.aiParadigms.table.aiRole": {"message": "AI Role"}, "ai101.aiParadigms.table.outcomes": {"message": "Learning Outcomes"}, "ai101.aiParadigms.table.colearning": {"message": "Human-AI Co-learning"}, "ai101.aiParadigms.table.colearning.principles": {"message": "Mutual learning, adaptation, shared mental models"}, "ai101.aiParadigms.table.colearning.human": {"message": "Collaborative partner, active participant, adapter"}, "ai101.aiParadigms.table.colearning.ai": {"message": "Learning partner, team member, adaptive agent"}, "ai101.aiParadigms.table.colearning.outcomes": {"message": "Enhanced team performance, human-AI collaboration skills"}, "ai101.aiParadigms.table.hcaif": {"message": "Human-Centric AI-First Education"}, "ai101.aiParadigms.table.hcaif.principles": {"message": "Human values priority, ethical application, personalization"}, "ai101.aiParadigms.table.hcaif.human": {"message": "Autonomous learner, ethical practitioner, problem solver"}, "ai101.aiParadigms.table.hcaif.ai": {"message": "Learning assistant, content generator, feedback provider"}, "ai101.aiParadigms.table.hcaif.outcomes": {"message": "Critical thinking, ethical awareness, real-world problem solving"}, "ai101.aiParadigms.table.irreplaceable": {"message": "Machine-Irreplaceable Skills"}, "ai101.aiParadigms.table.irreplaceable.principles": {"message": "Human-unique capabilities, creativity, ethical judgment"}, "ai101.aiParadigms.table.irreplaceable.human": {"message": "Developer of core human competencies"}, "ai101.aiParadigms.table.irreplaceable.ai": {"message": "Routine task assistant, higher-order thinking catalyst"}, "ai101.aiParadigms.table.irreplaceable.outcomes": {"message": "Core human competencies, AI-era competitive advantage"}, "ai101.aiParadigms.table.epistemological": {"message": "Epistemological & Adaptive"}, "ai101.aiParadigms.table.epistemological.principles": {"message": "Knowledge construction understanding, adaptability, lifelong learning"}, "ai101.aiParadigms.table.epistemological.human": {"message": "Active knowledge explorer, lifelong learner, change adapter"}, "ai101.aiParadigms.table.epistemological.ai": {"message": "Information assistant, learning path planner, analysis support"}, "ai101.aiParadigms.table.epistemological.outcomes": {"message": "Deep knowledge understanding, continuous learning ability, intrinsic motivation"}, "ai101.aiParadigms.synthesis.title": {"message": "Paradigm Synthesis"}, "ai101.aiParadigms.synthesis.content": {"message": "These paradigms are interconnected and represent a holistic shift toward:\n\n• Human-AI collaborative intelligence rather than replacement\n• Agency reconceptualization for both humans and AI\n• Ethical frameworks for human-AI learning relationships\n• Focus on \"why\" (purpose, ethics, values) as human contribution\n• Philosophy and ethical reasoning elevated across all domains"}, "ai101.endToEnd.title": {"message": "End-to-End Learning Paradigm"}, "ai101.endToEnd.subtitle": {"message": "端到端学习范式"}, "ai101.endToEnd.concept.title": {"message": "Core Concept"}, "ai101.endToEnd.concept.definition": {"message": "End-to-end learning is a deep learning approach that directly maps from raw input to final output through a single neural network model, without requiring manual intermediate feature extraction steps."}, "ai101.endToEnd.concept.coreIdea": {"message": "Core Idea: Let the model autonomously learn the optimal mapping relationship from input to output"}, "ai101.endToEnd.features.title": {"message": "Main Features"}, "ai101.endToEnd.features.directMapping": {"message": "Direct Mapping: Raw data → Deep neural network → Final result"}, "ai101.endToEnd.features.globalOptimization": {"message": "Global Optimization: Joint training globally, avoiding local optima of sub-modules"}, "ai101.endToEnd.features.autoFeature": {"message": "Automatic Feature Learning: No manual feature engineering, model learns representations autonomously"}, "ai101.endToEnd.features.taskDriven": {"message": "Task-Driven: Optimization strategy oriented towards the final goal"}, "ai101.endToEnd.advantages.title": {"message": "Technical Advantages"}, "ai101.endToEnd.advantages.advantage": {"message": "Advantage"}, "ai101.endToEnd.advantages.description": {"message": "Description"}, "ai101.endToEnd.advantages.simplified": {"message": "Simplified Architecture"}, "ai101.endToEnd.advantages.simplifiedDesc": {"message": "Reduce manual design, unified training and inference pipeline"}, "ai101.endToEnd.advantages.performance": {"message": "Performance Improvement"}, "ai101.endToEnd.advantages.performanceDesc": {"message": "Achieve SOTA in multiple domains, avoid error accumulation"}, "ai101.endToEnd.advantages.adaptability": {"message": "Adaptability"}, "ai101.endToEnd.advantages.adaptabilityDesc": {"message": "Automatically discover task-relevant features"}, "ai101.endToEnd.advantages.optimization": {"message": "End-to-End Optimization"}, "ai101.endToEnd.advantages.optimizationDesc": {"message": "Global optimization, direct gradient propagation"}, "ai101.quadrant.title": {"message": "Human-AI Interaction Quadrant Model"}, "ai101.quadrant.subtitle": {"message": "AI(<PERSON>, Don't Know) × Human(<PERSON>, Don't Know) Four-Quadrant Interaction Mode"}, "ai101.quadrant.model.title": {"message": "Four-Quadrant Interaction Model"}, "ai101.quadrant.human.know": {"message": "Human Knows"}, "ai101.quadrant.human.dontKnow": {"message": "Human Doesn't Know"}, "ai101.quadrant.ai.know": {"message": "AI Knows"}, "ai101.quadrant.ai.dontKnow": {"message": "AI Doesn't Know"}, "ai101.quadrant.q1.title": {"message": "Assignment & Validation"}, "ai101.quadrant.q1.desc": {"message": "AI assists with tasks. Humans validate the results to ensure accuracy and reliability."}, "ai101.quadrant.q2.title": {"message": "Query & Discovery"}, "ai101.quadrant.q2.desc": {"message": "AI answers questions and introduces new knowledge, helping expand human understanding."}, "ai101.quadrant.q3.title": {"message": "Teaching & Training"}, "ai101.quadrant.q3.desc": {"message": "Humans teach the AI by providing context and domain knowledge—through SFT (Supervised Fine-Tuning) or RAG (Retrieval-Augmented Generation)."}, "ai101.quadrant.q4.title": {"message": "Exploration & Innovation"}, "ai101.quadrant.q4.desc": {"message": "Humans and AI collaborate to explore uncharted territory, discovering and creating together."}, "ai101.quadrant.dynamics.title": {"message": "Dynamic Interaction Principles"}, "ai101.quadrant.dynamics.item1": {"message": "Knowledge boundaries are fluid and context-dependent"}, "ai101.quadrant.dynamics.item2": {"message": "Effective collaboration requires understanding each party's strengths"}, "ai101.quadrant.dynamics.item3": {"message": "The goal is complementary intelligence, not replacement"}, "ai101.quadrant.dynamics.item4": {"message": "Continuous learning and adaptation for both human and AI"}, "ai101.higherThinking.title": {"message": "Understanding Higher-Order Thinking in the AI Era"}, "ai101.higherThinking.subtitle": {"message": "From Information Recall to Deep Intellectual Engagement"}, "ai101.higherThinking.definition.title": {"message": "What is Higher-Order Thinking?"}, "ai101.higherThinking.definition.content": {"message": "Higher-order thinking skills encompass sophisticated cognitive processes that distinguish mere information recall from deep intellectual engagement, creativity, and critical analysis. These include analysis, evaluation, and creation—cognitive operations that require examining information critically, making judgments based on criteria, and synthesizing knowledge into novel configurations."}, "ai101.higherThinking.analysis.title": {"message": "Analysis"}, "ai101.higherThinking.analysis.content": {"message": "Breaking down complex information into components and understanding relationships"}, "ai101.higherThinking.evaluation.title": {"message": "Evaluation"}, "ai101.higherThinking.evaluation.content": {"message": "Making judgments based on criteria and standards, assessing quality and validity"}, "ai101.higherThinking.creation.title": {"message": "Creation"}, "ai101.higherThinking.creation.content": {"message": "Synthesizing knowledge into novel configurations and generating original ideas"}, "ai101.higherThinking.aiEra.title": {"message": "Reconceptualizing in the AI Era"}, "ai101.higherThinking.aiEra.item1": {"message": "Ability to evaluate AI-generated content critically"}, "ai101.higherThinking.aiEra.item2": {"message": "Understanding algorithmic limitations and biases"}, "ai101.higherThinking.aiEra.item3": {"message": "Maintaining human agency in technology-mediated environments"}, "ai101.higherThinking.aiEra.item4": {"message": "Metacognitive awareness of human-AI cognitive interactions"}, "ai101.cognitiveOffloading.title": {"message": "The Cognitive Offloading Phenomenon"}, "ai101.cognitiveOffloading.subtitle": {"message": "How AI Changes Human Thinking Processes"}, "ai101.cognitiveOffloading.definition.title": {"message": "What is Cognitive Offloading?"}, "ai101.cognitiveOffloading.definition.content": {"message": "Cognitive offloading represents the practice of using external tools or resources to reduce mental effort and enhance cognitive performance. This phenomenon encompasses the delegation of memory storage, computation, and increasingly, complex reasoning processes to AI systems."}, "ai101.cognitiveOffloading.traditional.title": {"message": "Traditional Offloading"}, "ai101.cognitiveOffloading.traditional.item1": {"message": "Note-taking and external memory"}, "ai101.cognitiveOffloading.traditional.item2": {"message": "Calculators for computation"}, "ai101.cognitiveOffloading.traditional.item3": {"message": "Maps for navigation"}, "ai101.cognitiveOffloading.traditional.item4": {"message": "Preserves higher-order thinking"}, "ai101.cognitiveOffloading.ai.title": {"message": "AI-Era Offloading"}, "ai101.cognitiveOffloading.ai.item1": {"message": "Complex analysis and synthesis"}, "ai101.cognitiveOffloading.ai.item2": {"message": "Creative content generation"}, "ai101.cognitiveOffloading.ai.item3": {"message": "Decision-making support"}, "ai101.cognitiveOffloading.ai.item4": {"message": "May impact skill development"}, "ai101.cognitiveOffloading.implications.title": {"message": "Implications for Learning"}, "ai101.cognitiveOffloading.implications.item1": {"message": "Risk of reduced opportunities for sustained analytical engagement"}, "ai101.cognitiveOffloading.implications.item2": {"message": "Potential impact on executive function development"}, "ai101.cognitiveOffloading.implications.item3": {"message": "Questions about intellectual autonomy and adaptability"}, "ai101.cognitiveOffloading.implications.item4": {"message": "Need for balanced human-AI cognitive partnerships"}, "ai101.responsibleFrameworks.title": {"message": "Frameworks for Responsible AI Integration"}, "ai101.responsibleFrameworks.subtitle": {"message": "Systematic Approaches to Preserve Higher-Order Thinking"}, "ai101.responsibleFrameworks.unesco.title": {"message": "UNESCO AI Competency Framework"}, "ai101.responsibleFrameworks.unesco.item1": {"message": "Human-centered mindset development"}, "ai101.responsibleFrameworks.unesco.item2": {"message": "AI ethics understanding"}, "ai101.responsibleFrameworks.unesco.item3": {"message": "AI techniques and applications mastery"}, "ai101.responsibleFrameworks.unesco.item4": {"message": "AI system design engagement"}, "ai101.responsibleFrameworks.schoolai.title": {"message": "SchoolAI 4 C's Framework"}, "ai101.responsibleFrameworks.schoolai.item1": {"message": "Understanding AI capabilities and limitations"}, "ai101.responsibleFrameworks.schoolai.item2": {"message": "Using AI as a learning partner"}, "ai101.responsibleFrameworks.schoolai.item3": {"message": "Evaluating AI outputs critically"}, "ai101.responsibleFrameworks.schoolai.item4": {"message": "Leveraging AI for creative purposes"}, "ai101.responsibleFrameworks.extraheric.title": {"message": "FunBlocks AI Framework"}, "ai101.responsibleFrameworks.extraheric.content": {"message": "Unlike traditional human-AI interaction designs that replace human cognition, FunBlocks AI fosters cognitive engagement by posing questions or providing alternative perspectives rather than direct answers. This approach ensures students remain actively engaged in analytical thinking while benefiting from AI's capabilities."}, "ai101.responsibleFrameworks.samr.title": {"message": "SAMR Model for AI Integration"}, "ai101.responsibleFrameworks.samr.item1": {"message": "AI replaces traditional tools"}, "ai101.responsibleFrameworks.samr.item2": {"message": "AI enhances existing processes"}, "ai101.responsibleFrameworks.samr.item3": {"message": "AI enables significant task redesign"}, "ai101.responsibleFrameworks.samr.item4": {"message": "AI creates previously impossible learning experiences"}, "ai101.collaboration.title": {"message": "Human-AI Collaboration Models"}, "ai101.collaboration.subtitle": {"message": "Complementary Cognitive Strengths for Optimal Outcomes"}, "ai101.collaboration.complementary.title": {"message": "Complementary Strengths"}, "ai101.collaboration.human": {"message": "Human Strengths"}, "ai101.collaboration.ai": {"message": "AI Strengths"}, "ai101.collaboration.human.item1": {"message": "Novel and creative ideas"}, "ai101.collaboration.ai.item1": {"message": "Practical, implementable solutions"}, "ai101.collaboration.human.item2": {"message": "Contextual understanding"}, "ai101.collaboration.ai.item2": {"message": "Large-scale data processing"}, "ai101.collaboration.human.item3": {"message": "Ethical reasoning"}, "ai101.collaboration.ai.item3": {"message": "Pattern recognition"}, "ai101.collaboration.human.item4": {"message": "Emotional intelligence"}, "ai101.collaboration.ai.item4": {"message": "Consistent performance"}, "ai101.collaboration.creative.title": {"message": "AI as Creative Partner"}, "ai101.collaboration.creative.item1": {"message": "Generate diverse ideas and perspectives"}, "ai101.collaboration.creative.item2": {"message": "Challenge assumptions and biases"}, "ai101.collaboration.creative.item3": {"message": "Provide alternative viewpoints"}, "ai101.collaboration.creative.item4": {"message": "Support ideation and brainstorming"}, "ai101.collaboration.metacognitive.title": {"message": "Metacognitive Scaffolding"}, "ai101.collaboration.metacognitive.item1": {"message": "Analytics on learning patterns"}, "ai101.collaboration.metacognitive.item2": {"message": "Insights into cognitive processes"}, "ai101.collaboration.metacognitive.item3": {"message": "Reflection support and guidance"}, "ai101.collaboration.metacognitive.item4": {"message": "Strategy recommendation"}, "ai101.collaboration.division.title": {"message": "Optimal Division of Cognitive Labor"}, "ai101.collaboration.division.content": {"message": "AI Handles: Routine processing, data analysis, initial content generation\n              \nHuman Focuses: Critical evaluation, creative synthesis, ethical reasoning, final decisions\n\nResult: Enhanced cognitive outcomes through complementary collaboration"}, "ai101.educationalPractice.title": {"message": "Implications for Educational Practice"}, "ai101.educationalPractice.subtitle": {"message": "Reconceptualizing Pedagogy, Assessment, and Academic Integrity"}, "ai101.educationalPractice.assignment.title": {"message": "Assignment Design"}, "ai101.educationalPractice.assignment.item1": {"message": "Clear learning objectives communication"}, "ai101.educationalPractice.assignment.item2": {"message": "Specify appropriate AI usage guidelines"}, "ai101.educationalPractice.assignment.item3": {"message": "Require critical analysis of AI outputs"}, "ai101.educationalPractice.assignment.item4": {"message": "Demonstrate original thinking integration"}, "ai101.educationalPractice.integrity.title": {"message": "Academic Integrity"}, "ai101.educationalPractice.integrity.item1": {"message": "Transparency about AI usage required"}, "ai101.educationalPractice.integrity.item2": {"message": "Emphasis on original thinking importance"}, "ai101.educationalPractice.integrity.item3": {"message": "Student responsibility for all submitted content"}, "ai101.educationalPractice.integrity.item4": {"message": "Nuanced policies distinguishing appropriate use"}, "ai101.educationalPractice.tilt.title": {"message": "TILT Framework for AI-Integrated Assignments"}, "ai101.educationalPractice.tilt.component": {"message": "Component"}, "ai101.educationalPractice.tilt.description": {"message": "Description"}, "ai101.educationalPractice.tilt.transparency": {"message": "Clear communication of learning objectives and AI usage expectations"}, "ai101.educationalPractice.tilt.instruction": {"message": "Explicit guidance on appropriate AI applications and limitations"}, "ai101.educationalPractice.tilt.learning": {"message": "Focus on learning outcomes and skill development"}, "ai101.educationalPractice.tilt.testing": {"message": "Assessment criteria that value critical thinking and originality"}, "ai101.educationalPractice.curriculum.title": {"message": "AI Literacy Curriculum Development"}, "ai101.educationalPractice.curriculum.item1": {"message": "Integrate AI competencies across disciplines, not as separate subject"}, "ai101.educationalPractice.curriculum.item2": {"message": "Foster interdisciplinary learning connecting STEM and social studies"}, "ai101.educationalPractice.curriculum.item3": {"message": "Develop understanding of AI capabilities, limitations, and ethics"}, "ai101.educationalPractice.curriculum.item4": {"message": "Emphasize critical evaluation and responsible AI use"}, "ai_mindmap.head.title": {"message": "FunBlocks AI Mindmap Extension - Transform Web Content into Mind Maps"}, "ai_mindmap.head.description": {"message": "Transform any web page or YouTube video into visual mind maps with AI. One-click brainstorming, critical thinking, and seamless integration with FunBlocks AIFlow. Free Chrome extension."}, "ai_mindmap.head.keywords": {"message": "AI mindmap, mind mapping, Chrome extension, web content analysis, YouTube transcript, brainstorming, critical thinking, AIFlow, visual learning, knowledge mapping"}, "ai_mindmap.hero.badge": {"message": "NEW CHROME EXTENSION"}, "ai_mindmap.hero.title": {"message": "Transform Any Web Content into Mind Maps with AI"}, "ai_mindmap.hero.subtitle": {"message": "One-click mind mapping from web pages, YouTube videos, and AI conversations. Your gateway to FunBlocks AIFlow."}, "ai_mindmap.hero.download": {"message": "Download Extension for FREE"}, "ai_mindmap.hero.learn_more": {"message": "See How It Works"}, "ai_mindmap.hero.stat1": {"message": "Free Mind Maps"}, "ai_mindmap.hero.stat2": {"message": "Daily Free Uses"}, "ai_mindmap.hero.stat3": {"message": "User Rating"}, "ai_mindmap.hero.image_caption": {"message": "Click to enlarge"}, "ai_mindmap.benefits.title": {"message": "Key Benefits"}, "ai_mindmap.benefits.description": {"message": "Discover how FunBlocks AI Mindmap Extension transforms your browsing experience with these powerful benefits:"}, "ai_mindmap.benefits.benefit1.title": {"message": "Visual Learning Enhancement"}, "ai_mindmap.benefits.benefit1.description": {"message": "Transform complex information into visual mind maps that improve comprehension and memory retention."}, "ai_mindmap.benefits.benefit2.title": {"message": "Instant Knowledge Extraction"}, "ai_mindmap.benefits.benefit2.description": {"message": "Extract key insights from web pages and videos in seconds, saving hours of manual note-taking."}, "ai_mindmap.benefits.benefit3.title": {"message": "Enhanced Focus & Clarity"}, "ai_mindmap.benefits.benefit3.description": {"message": "Organize scattered information into clear, structured mind maps that highlight important connections."}, "ai_mindmap.benefits.benefit4.title": {"message": "Seamless Workflow Integration"}, "ai_mindmap.benefits.benefit4.description": {"message": "Start with any web content and seamlessly transition to deep exploration in FunBlocks AIFlow."}, "ai_mindmap.benefits.benefit5.title": {"message": "AI-Powered Insights"}, "ai_mindmap.benefits.benefit5.description": {"message": "Leverage AI to discover hidden patterns and generate new ideas from existing content."}, "ai_mindmap.benefits.benefit6.title": {"message": "Always Available"}, "ai_mindmap.benefits.benefit6.description": {"message": "Access powerful mind mapping capabilities wherever you browse, whenever inspiration strikes."}, "ai_mindmap.how_it_works.title": {"message": "How AI Mindmap Works"}, "ai_mindmap.how_it_works.description": {"message": "Transform any web content into visual mind maps with just one click. Perfect for learning, research, and creative thinking."}, "ai_mindmap.how_it_works.step1.title": {"message": "Install the Extension"}, "ai_mindmap.how_it_works.step1.description": {"message": "Add AI Mindmap to Chrome in seconds. No complex setup required."}, "ai_mindmap.how_it_works.step2.title": {"message": "Browse Any Website"}, "ai_mindmap.how_it_works.step2.description": {"message": "Visit any webpage or YouTube video and click the AI Mindmap icon to extract content."}, "ai_mindmap.how_it_works.step3.title": {"message": "Generate Mind Maps"}, "ai_mindmap.how_it_works.step3.description": {"message": "Watch as AI transforms complex content into clear, visual mind maps instantly."}, "ai_mindmap.how_it_works.feature1.badge": {"message": "WEB CONTENT"}, "ai_mindmap.how_it_works.feature1.title": {"message": "One-Click Web Page Mind Mapping"}, "ai_mindmap.how_it_works.feature1.description": {"message": "Transform any article, blog post, or web page into a structured mind map. Perfect for research, learning, and content analysis."}, "ai_mindmap.how_it_works.feature1.point1": {"message": "Automatically extract key concepts and main ideas from web pages"}, "ai_mindmap.how_it_works.feature1.point2": {"message": "Organize information hierarchically for better understanding"}, "ai_mindmap.how_it_works.feature1.point3": {"message": "Save to FunBlocks AIFlow for further exploration and development"}, "ai_mindmap.how_it_works.feature1.image_caption": {"message": "Click to enlarge"}, "ai_mindmap.how_it_works.feature2.badge": {"message": "YOUTUBE VIDEOS"}, "ai_mindmap.how_it_works.feature2.title": {"message": "YouTube Video Mind Maps"}, "ai_mindmap.how_it_works.feature2.description": {"message": "Convert YouTube video transcripts into visual mind maps. Perfect for educational videos, lectures, and tutorials."}, "ai_mindmap.how_it_works.feature2.point1": {"message": "Extract key points from video transcripts automatically"}, "ai_mindmap.how_it_works.feature2.point2": {"message": "Perfect for educational content and online learning"}, "ai_mindmap.how_it_works.feature2.point3": {"message": "Save hours of note-taking with instant visual summaries"}, "ai_mindmap.how_it_works.feature2.image_caption": {"message": "Click to enlarge"}, "ai_mindmap.how_it_works.feature3.badge": {"message": "AI BRAINSTORMING"}, "ai_mindmap.how_it_works.feature3.title": {"message": "AI-Powered Brainstorming & Analysis"}, "ai_mindmap.how_it_works.feature3.description": {"message": "Generate mind maps from any topic, perform critical analysis, and explore complex subjects with AI assistance."}, "ai_mindmap.how_it_works.feature3.point1": {"message": "AI brainstorming with classic thinking models"}, "ai_mindmap.how_it_works.feature3.point2": {"message": "Critical analysis and decision-making support"}, "ai_mindmap.how_it_works.feature3.point3": {"message": "Break down complex topics for better understanding"}, "ai_mindmap.how_it_works.feature3.image_caption": {"message": "Click to enlarge"}, "ai_mindmap.aiflow_integration.title": {"message": "Seamless Integration with FunBlocks AIFlow"}, "ai_mindmap.aiflow_integration.description": {"message": "AI Mindmap serves as your quick entry point to the powerful FunBlocks AIFlow ecosystem. Start anywhere, explore everywhere."}, "ai_mindmap.aiflow_integration.gateway.title": {"message": "Your Gateway to AIFlow"}, "ai_mindmap.aiflow_integration.gateway.description": {"message": "Transform web browsing into active learning. Every mind map you create can be saved and expanded in FunBlocks AIFlow for deeper exploration."}, "ai_mindmap.aiflow_integration.gateway.point1": {"message": "One-click save to FunBlocks AIFlow workspace"}, "ai_mindmap.aiflow_integration.gateway.point2": {"message": "Seamless transition from browser to full AIFlow experience"}, "ai_mindmap.aiflow_integration.gateway.point3": {"message": "Continue exploration with advanced thinking tools"}, "ai_mindmap.aiflow_integration.gateway.image_caption": {"message": "Click to enlarge"}, "ai_mindmap.video.title": {"message": "See AI Mindmap in Action"}, "ai_mindmap.video.description": {"message": "Watch how FunBlocks AI Mindmap transforms web content into visual mind maps"}, "ai_mindmap.video.cta": {"message": "Try It Yourself"}, "ai_mindmap.video.feature1.title": {"message": "Visual Learning"}, "ai_mindmap.video.feature1.description": {"message": "Watch how web content transforms into structured mind maps instantly"}, "ai_mindmap.video.feature2.title": {"message": "AI Brainstorming"}, "ai_mindmap.video.feature2.description": {"message": "See how AI-powered thinking frameworks enhance your brainstorming process"}, "ai_mindmap.video.feature3.title": {"message": "AIFlow Integration"}, "ai_mindmap.video.feature3.description": {"message": "Discover seamless integration with FunBlocks AIFlow for deeper exploration"}, "ai_mindmap.cta.title": {"message": "Start Your Visual Learning Journey Today"}, "ai_mindmap.cta.description": {"message": "Transform how you learn, research, and think with AI-powered mind mapping. Join thousands of users who've already enhanced their learning experience."}, "ai_mindmap.cta.download": {"message": "Download Extension"}, "ai_mindmap.cta.explore": {"message": "Explore AIFlow"}, "ai_mindmap.cta.note": {"message": "✨ Free to start • No credit card required • Install in seconds"}, "ai_mindmap.testimonials.title": {"message": "What Our Users Say"}, "ai_mindmap.testimonials.description": {"message": "Join thousands of satisfied users who are transforming how they learn and work"}, "ai_mindmap.testimonials.user1.name": {"message": "<PERSON>"}, "ai_mindmap.testimonials.user1.role": {"message": "Graduate Student"}, "ai_mindmap.testimonials.user1.text": {"message": "This extension is a game-changer for research! I use it to create mind maps from academic papers and lecture videos. The AI's ability to extract key concepts is impressive."}, "ai_mindmap.testimonials.user2.name": {"message": "<PERSON>"}, "ai_mindmap.testimonials.user2.role": {"message": "Content Creator"}, "ai_mindmap.testimonials.user2.text": {"message": "FunBlocks AI Mindmap has transformed how I organize content ideas. The instant mind maps from web research save me hours of work every week!"}, "ai_mindmap.testimonials.user3.name": {"message": "Prof. <PERSON>"}, "ai_mindmap.testimonials.user3.role": {"message": "University Lecturer"}, "ai_mindmap.testimonials.user3.text": {"message": "I recommend this tool to all my students. It helps them visualize complex topics and improves their understanding of course materials."}, "ai_mindmap.testimonials.user4.name": {"message": "<PERSON>"}, "ai_mindmap.testimonials.user4.role": {"message": "High School Student"}, "ai_mindmap.testimonials.user4.text": {"message": "Love how it turns YouTube educational videos into mind maps! Makes studying so much easier and more fun."}, "ai_mindmap.testimonials.user5.name": {"message": "<PERSON>"}, "ai_mindmap.testimonials.user5.role": {"message": "Business Analyst"}, "ai_mindmap.testimonials.user5.text": {"message": "Perfect for analyzing market research and competitor websites. The integration with AIFlow makes it a powerful tool for business intelligence."}, "ai_mindmap.testimonials.user6.name": {"message": "Dr. <PERSON>"}, "ai_mindmap.testimonials.user6.role": {"message": "Research Scientist"}, "ai_mindmap.testimonials.user6.text": {"message": "The AI's ability to identify connections between concepts is remarkable. It's become an essential tool for my literature reviews."}, "ai_mindmap.faq.q1": {"message": "What is the FunBlocks AI Mindmap Extension?"}, "ai_mindmap.faq.a1": {"message": "The FunBlocks AI Mindmap Extension is a Chrome browser extension that transforms any web page or YouTube video into visual mind maps using AI. It helps with learning, research, and knowledge organization by extracting key concepts and presenting them in an easy-to-understand visual format."}, "ai_mindmap.faq.q2": {"message": "How does the AI Mindmap Extension work?"}, "ai_mindmap.faq.a2": {"message": "Simply install the extension, visit any webpage or YouTube video, and click the AI Mindmap icon. The extension will automatically extract key concepts and create a structured mind map that you can save to FunBlocks AIFlow for further exploration."}, "ai_mindmap.faq.q3": {"message": "Can I use the extension with YouTube videos?"}, "ai_mindmap.faq.a3": {"message": "Yes! The extension can extract transcripts from YouTube videos and convert them into mind maps, making it perfect for educational content, lectures, and tutorials. This saves you hours of manual note-taking."}, "ai_mindmap.faq.q4": {"message": "Is the AI Mindmap Extension free?"}, "ai_mindmap.faq.a4": {"message": "Yes, the extension is free to download and use. New users get 30 free mind map generations, plus 10 free uses daily. It's also included in all FunBlocks AI subscription plans for unlimited usage."}, "ai_mindmap.faq.q5": {"message": "How does it integrate with FunBlocks AIFlow?"}, "ai_mindmap.faq.a5": {"message": "The AI Mindmap Extension serves as a gateway to FunBlocks AIFlow. You can save any mind map directly to your AIFlow workspace for deeper exploration, brainstorming, and access to advanced thinking tools like critical analysis and creative thinking frameworks."}, "ai_mindmap.faq.q6": {"message": "What browsers are supported?"}, "ai_mindmap.faq.a6": {"message": "Currently, the extension supports Chrome and Edge browsers. We plan to expand to other browsers in the future based on user demand."}, "ai_mindmap.faq.q7": {"message": "Can I brainstorm new ideas with the extension?"}, "ai_mindmap.faq.a7": {"message": "Absolutely! Beyond analyzing existing content, the extension includes AI-powered brainstorming tools that can generate mind maps on any topic, perform critical analysis, and help with decision-making using classic thinking models."}, "ai_mindmap.faq.q8": {"message": "What makes this different from other mind mapping tools?"}, "ai_mindmap.faq.a8": {"message": "Our extension uniquely combines AI-powered content extraction, seamless browser integration, and direct connection to the powerful FunBlocks AIFlow ecosystem. It's not just a mind mapping tool—it's your gateway to enhanced thinking and learning."}, "ai101.slide17.longTerm.item3": {"message": "Shape the future of human-AI collaboration"}, "ai101.hallucination.title": {"message": "LLM Hallucination"}, "ai101.hallucination.subtitle": {"message": "What is Hallucination?"}, "ai101.hallucination.definition.title": {"message": "Definition"}, "ai101.hallucination.definition.content": {"message": "Models generate seemingly plausible but actually inaccurate or non-existent information"}, "ai101.hallucination.types.title": {"message": "Main Types of Hallucination"}, "ai101.hallucination.types.factual.title": {"message": "Factual Hallucination"}, "ai101.hallucination.types.factual.item1": {"message": "False information: Generating non-existent historical events, people, or data"}, "ai101.hallucination.types.factual.item2": {"message": "Fake citations: Fabricating non-existent academic papers, website links"}, "ai101.hallucination.types.factual.item3": {"message": "Numerical errors: Providing incorrect statistics, dates, quantities"}, "ai101.hallucination.types.logical.title": {"message": "Logical Hallucination"}, "ai101.hallucination.types.logical.item1": {"message": "Reasoning errors: Fallacies in logical deduction"}, "ai101.hallucination.types.logical.item2": {"message": "Causal confusion: Incorrectly establishing causal relationships"}, "ai101.hallucination.types.logical.item3": {"message": "Self-contradiction: Contradictory statements within the same response"}, "ai101.hallucination.types.creative.title": {"message": "Creative Hallucination"}, "ai101.hallucination.types.creative.item1": {"message": "Fictional content: Creating non-existent stories, characters, works"}, "ai101.hallucination.types.creative.item2": {"message": "Mixed information: Incorrectly combining information from different sources"}, "ai101.hallucination.causes.title": {"message": "Causes"}, "ai101.hallucination.causes.training.title": {"message": "Training Data Issues"}, "ai101.hallucination.causes.training.item1": {"message": "Errors in training data"}, "ai101.hallucination.causes.training.item2": {"message": "Incomplete training coverage"}, "ai101.hallucination.causes.training.item3": {"message": "Outdated or contradictory information"}, "ai101.hallucination.causes.model.title": {"message": "Model Mechanism Limitations"}, "ai101.hallucination.causes.model.item1": {"message": "Probability-based generation"}, "ai101.hallucination.causes.model.item2": {"message": "Lack of real-world knowledge verification"}, "ai101.hallucination.causes.model.item3": {"message": "Context understanding limitations"}, "ai101.hallucination.strategies.title": {"message": "Identification and Prevention Strategies"}, "ai101.hallucination.strategies.user.title": {"message": "User Level"}, "ai101.hallucination.strategies.user.item1": {"message": "Cross-verification: Verify important information from multiple sources"}, "ai101.hallucination.strategies.user.item2": {"message": "Critical thinking: Maintain skepticism, especially for specific data"}, "ai101.hallucination.strategies.user.item3": {"message": "Professional judgment: Rely on authoritative resources in professional fields"}, "ai101.hallucination.strategies.technical.title": {"message": "Technical Level"}, "ai101.hallucination.strategies.technical.item1": {"message": "Retrieval Augmented Generation (RAG): Combine with real-time knowledge base"}, "ai101.hallucination.strategies.technical.item2": {"message": "Multi-model verification: Cross-verify using multiple models"}, "ai101.hallucination.strategies.technical.item3": {"message": "Confidence assessment: Label answer reliability"}, "ai101.hallucination.keyPoints.title": {"message": "Key Points"}, "ai101.hallucination.keyPoints.content": {"message": "🚨 Remember: Large language models are powerful tools, but require human judgment and verification to ensure information accuracy"}, "ai101.workTransformation.title": {"message": "Great Transformation: AI Reshaping Work and Life"}, "ai101.workTransformation.subtitle": {"message": "Paradigm Shift: From \"Job Replacement\" to \"Task Reconstruction\""}, "ai101.workTransformation.globalChange.title": {"message": "Global Labor Market Upheaval"}, "ai101.workTransformation.globalChange.content": {"message": "Core shift: From \"jobs\" to \"tasks\" - AI automates specific tasks (30%-70%) within jobs, not entire professions."}, "ai101.workTransformation.aiPlusX.title": {"message": "AI+X Composite Talent"}, "ai101.workTransformation.aiPlusX.content": {"message": "Emergence of \"AI+X\" professionals who combine domain expertise with AI capabilities, emphasizing lifelong learning for career resilience."}, "ai101.workTransformation.example.title": {"message": "Legal Industry Example"}, "ai101.workTransformation.example.content": {"message": "AI automates: Legal research, document review, contract drafting\nLawyers focus on: Complex litigation strategy, client empathy, ethical reasoning, creative solutions\nResult: Career paths become dynamic \"skill lattices\" rather than linear ladders"}, "ai101.humanCompetencies.title": {"message": "Human Core Competencies: Value Anchors in the Post-Automation World"}, "ai101.humanCompetencies.subtitle": {"message": "Value Shift: From \"What to Do\" to \"How to Think and Collaborate\""}, "ai101.humanCompetencies.cognitive.title": {"message": "Higher-Order Cognitive Abilities"}, "ai101.humanCompetencies.cognitive.item1": {"message": "Analytical thinking and creative thinking"}, "ai101.humanCompetencies.cognitive.item2": {"message": "Meta-cognitive skills and learning strategies"}, "ai101.humanCompetencies.social.title": {"message": "Social-Emotional Abilities"}, "ai101.humanCompetencies.social.item1": {"message": "Leadership, collaboration, communication skills"}, "ai101.humanCompetencies.social.item2": {"message": "Empathy, persuasion, and motivation abilities"}, "ai101.humanCompetencies.personal.title": {"message": "Personal Traits"}, "ai101.humanCompetencies.personal.item1": {"message": "Resilience, flexibility, agility, self-motivation"}, "ai101.humanCompetencies.personal.item2": {"message": "AI and big data literacy"}, "ai101.humanCompetencies.metaphor.title": {"message": "Core Metaphor: AI as \"Cognitive Exoskeleton\""}, "ai101.humanCompetencies.metaphor.content": {"message": "Relationship reconstruction: From \"human-machine opposition\" to \"human-machine symbiosis\"\nGoal: Liberate humans from repetitive cognitive labor, focus on higher-order creation and strategic thinking"}, "ai101.aiCapabilities.title": {"message": "New Capabilities: AI Enhancing Human Achievement"}, "ai101.aiCapabilities.subtitle": {"message": "Enhancement Engine: Expanding New Frontiers of Human Achievement"}, "ai101.aiCapabilities.science.title": {"message": "Revolutionizing Scientific Discovery"}, "ai101.aiCapabilities.science.item1": {"message": "AlphaFold: Solved protein folding, predicted 200M+ protein structures, saving ~1 billion years of research time"}, "ai101.aiCapabilities.science.item2": {"message": "MatterGen: \"Reverse design\" new materials, AI proposes 120K candidate structures in 1 hour"}, "ai101.aiCapabilities.science.item3": {"message": "Scientists evolve from \"experimenters\" to \"hypothesis strategists\" and \"inquiry designers\""}, "ai101.aiCapabilities.arts.title": {"message": "Catalyzing New Renaissance: Arts, Music & Design"}, "ai101.aiCapabilities.arts.item1": {"message": "AI Music (MuseNet): Assists composition, arrangement, generates emotion-specific music from text"}, "ai101.aiCapabilities.arts.item2": {"message": "AI Art (Midjourney): Lowers visual expression barriers, becomes artists' \"creative accelerator\""}, "ai101.aiCapabilities.arts.item3": {"message": "Creator value shifts from \"technical execution\" to \"conceptual planning\" and \"aesthetic judgment\""}, "ai101.aiCapabilities.personalization.title": {"message": "Personalized World: From Precision Medicine to Customized Experiences"}, "ai101.aiCapabilities.personalization.item1": {"message": "Precision Medicine: Customized treatment plans based on individual genes and imaging data"}, "ai101.aiCapabilities.personalization.item2": {"message": "Customized Consumption: E-commerce and streaming create unique experiences for each user"}, "ai101.aiCapabilities.personalization.item3": {"message": "Adaptive Learning: Dynamically adjusts teaching content based on student progress, achieving \"personalized education\""}, "ai101.educationalRevolution.title": {"message": "Rethinking: Educational Revolution in the AI Era"}, "ai101.educationalRevolution.subtitle": {"message": "Educational Renaissance: Comprehensive Reshaping of Goals, Content, and Methods"}, "ai101.educationalRevolution.newGoals.title": {"message": "New North Star: Redefining Educational Purpose"}, "ai101.educationalRevolution.newGoals.item1": {"message": "From \"knowledge transmission\" to \"competency cultivation\": When knowledge is readily available, education returns to \"moral education\" essence"}, "ai101.educationalRevolution.newGoals.item2": {"message": "From \"knowing\" to \"becoming\": Goal is cultivating \"lifelong learners\" who can learn autonomously, solve complex problems, and make ethical judgments"}, "ai101.educationalRevolution.newGoals.item3": {"message": "Core competency framework: Countries propose \"four-in-one\" competency models integrating \"knowledge, thinking, values, practice\""}, "ai101.educationalRevolution.deconstructed.title": {"message": "Deconstructed Classroom: Curriculum and Pedagogy"}, "ai101.educationalRevolution.deconstructed.item1": {"message": "Pedagogical shift: Project-based learning (PBL) and inquiry-based learning as core"}, "ai101.educationalRevolution.deconstructed.item2": {"message": "Curriculum reconstruction: From subject-based to competency-based, emphasizing interdisciplinary integration and real-world problem solving"}, "ai101.educationalRevolution.deconstructed.item3": {"message": "Learning space transformation: From fixed classrooms to flexible learning environments, supporting personalized and collaborative learning"}, "ai618.outline.title": {"message": "Outline"}, "ai618.outline.item1": {"message": "Generative AI Key Technologies and Characteristics"}, "ai618.outline.item2": {"message": "Human-AI Collaboration in the AI Era"}, "ai618.outline.item3": {"message": "Educational Revolution in the AI Era"}, "ai618.outline.item4": {"message": "AI and Higher-Order Thinking Skills Development"}, "ai618.outline.item5": {"message": "Cases and Operations"}, "ai618.section1.title": {"message": "Generative AI Key Technologies and Characteristics"}, "ai618.section1.subtitle": {"message": "Understanding the Core Technologies and Characteristics"}, "ai618.section2.title": {"message": "Human-AI Collaboration in the AI Era"}, "ai618.section2.subtitle": {"message": "Human-AI Collaboration in the AI Era"}, "ai618.section3.title": {"message": "Educational Revolution in the AI Era"}, "ai618.section3.subtitle": {"message": "Educational Revolution in the AI Era"}, "ai618.section4.title": {"message": "AI and Higher-Order Thinking Skills Development"}, "ai618.section4.subtitle": {"message": "AI and Higher-Order Thinking Skills Development"}, "ai618.section5.title": {"message": "Cases and Operations"}, "ai618.section5.subtitle": {"message": "Cases and Practical Operations"}, "ai101.teacherRoles.title": {"message": "Teacher New Roles and Assessment Paradigms"}, "ai101.teacherRoles.subtitle": {"message": "From Knowledge Transmitters to Learning Experience Designers"}, "ai101.teacherRoles.designer.title": {"message": "Teacher as \"Learning Designer\""}, "ai101.teacherRoles.designer.item1": {"message": "Role transformation: From \"knowledge transmitter\" to \"learning experience facilitator, guide, and designer\""}, "ai101.teacherRoles.designer.item2": {"message": "Value enhancement: AI handles repetitive tasks (grading, Q&A), teachers focus on thought inspiration and emotional care"}, "ai101.teacherRoles.designer.item3": {"message": "Capability requirements: Must possess high-level AI literacy and instructional design abilities, teacher training is key"}, "ai101.teacherRoles.assessment.title": {"message": "Assessment for Growth: Beyond Standardized Testing"}, "ai101.teacherRoles.assessment.item1": {"message": "Functional transformation: From \"selection and ranking\" to \"diagnosis and empowerment\""}, "ai101.teacherRoles.assessment.item2": {"message": "Method diversification: Growth portfolios, performance assessment, multi-stakeholder evaluation"}, "ai101.teacherRoles.assessment.item3": {"message": "AI assistance: Generate \"student digital portraits,\" dynamically track competency development, provide formative feedback"}, "ai101.aiTools.title": {"message": "New Tools: AI-Driven Educational Product Ecosystem"}, "ai101.aiTools.subtitle": {"message": "From \"Content Distributors\" to \"Cognitive Partners\""}, "ai101.aiTools.adaptive.title": {"message": "Intelligent Tutoring & Adaptive Learning Platforms"}, "ai101.aiTools.adaptive.content": {"message": "Achieve large-scale \"personalized education\""}, "ai101.aiTools.copilot.title": {"message": "Teacher & Administrator \"Co-pilots\""}, "ai101.aiTools.copilot.content": {"message": "AI assists lesson planning, question generation, scheduling, reducing teacher workload and increasing efficiency"}, "ai101.aiTools.immersive.title": {"message": "Immersive & Experiential Learning Environments"}, "ai101.aiTools.immersive.content": {"message": "VR/AR virtual experiments, historical scene recreation, enhancing learning engagement"}, "ai101.aiTools.content.title": {"message": "Content Interaction & Analysis Tools"}, "ai101.aiTools.content.content": {"message": "Chat with PDF documents, summarize key points, organize notes"}, "ai101.aiTools.collaborative.title": {"message": "Collaborative Learning Support Platforms"}, "ai101.aiTools.collaborative.content": {"message": "AI-assisted intelligent grouping, discussion guidance, enhancing team collaboration"}, "ai101.aiTools.thinking.title": {"message": "\"Cognitive Gym\": Actively Promoting Critical & Creative Thinking"}, "ai101.aiTools.thinking.content": {"message": "Create and manage \"beneficial cognitive friction\" rather than pursuing convenience, preventing \"cognitive outsourcing\""}, "ai101.riskManagement.title": {"message": "Risk Management: Navigating the Double-Edged Sword"}, "ai101.riskManagement.subtitle": {"message": "Avoiding Cognitive Outsourcing & Building Co-governance Models"}, "ai101.riskManagement.cognitiveOutsourcing.title": {"message": "Cognitive Outsourcing Risk"}, "ai101.riskManagement.cognitiveOutsourcing.item1": {"message": "Paradox: AI is both a \"cognitive gym\" and potentially a \"cognitive crutch\""}, "ai101.riskManagement.cognitiveOutsourcing.item2": {"message": "Key variable: Impact depends not on AI tools themselves, but on their \"pedagogical context\""}, "ai101.riskManagement.cognitiveOutsourcing.item3": {"message": "Solution: Systematic teacher training to master instructional design that uses AI to promote higher-order thinking"}, "ai101.strategicRecommendations.title": {"message": "Planning the Future: Strategic Recommendations and Action Plans"}, "ai101.strategicRecommendations.subtitle": {"message": "Strategic Recommendations for an AI-Ready Future"}, "ai101.strategicRecommendations.policyMakers.title": {"message": "For National & Regional Policy Makers"}, "ai101.strategicRecommendations.policyMakers.item1": {"message": "Develop national-level \"AI & Education\" integration strategy with systematic planning"}, "ai101.strategicRecommendations.policyMakers.item2": {"message": "Launch \"Future Skills\" national lifelong learning programs to support workforce transformation"}, "ai101.strategicRecommendations.policyMakers.item3": {"message": "Establish agile AI education governance frameworks using \"regulatory sandbox\" adaptive models"}, "ai101.strategicRecommendations.educators.title": {"message": "For Educational Institution Leaders"}, "ai101.strategicRecommendations.educators.item1": {"message": "Place teacher professional development at strategic core, support teacher transformation to \"learning designers\""}, "ai101.strategicRecommendations.educators.item2": {"message": "Promote systematic reconstruction of curriculum and assessment, shifting from \"knowledge-based\" to \"competency-based\""}, "ai101.strategicRecommendations.educators.item3": {"message": "Adopt rigorous AI product ethical procurement frameworks, prioritizing educational values and ethics"}, "ai101.slide18.clearPrompting.title": {"message": "Lazy Prompting"}, "ai101.slide18.clearPrompting.content": {"message": "Clear objectives, minimal instructions, with rich contextual information"}, "ai101.slide18.iterativeRefinement.title": {"message": "Iterative Refinement"}, "ai101.slide18.iterativeRefinement.content": {"message": "Continuously improve results through feedback and refinement"}, "ai101.slide18.rolePlaying.title": {"message": "Role Playing"}, "ai101.slide18.rolePlaying.content": {"message": "Guide AI to adopt specific roles or perspectives"}, "ai101.slide18.patterns.title": {"message": "Why Lazy Prompting?"}, "ai101.slide18.patterns.content": {"message": "Because too much micro-management can hold back AI. Too specific instructions reflect your own biases and limited skills, which may limit AI's creativity and professional capabilities. In many ways, AI may already exceed human capabilities and expectations"}, "ai101.slide24.cognitiveAugmentation.title": {"message": "Cognitive Augmentation"}, "ai101.slide24.cognitiveAugmentation.content": {"message": "Enhance human thinking capabilities with AI assistance"}, "ai101.slide24.patternRecognition.title": {"message": "Pattern Recognition"}, "ai101.slide24.patternRecognition.content": {"message": "Identify complex patterns and relationships from multiple perspectives"}, "ai101.slide24.speedScale.title": {"message": "Speed and Scale"}, "ai101.slide24.speedScale.content": {"message": "Process and analyze information at unprecedented speed"}, "ai101.slide24.limitations.title": {"message": "Human Cognitive Limitations"}, "ai101.slide24.limitations.item1": {"message": "Working memory constraints"}, "ai101.slide24.limitations.item2": {"message": "Confirmation bias and cognitive biases"}, "ai101.slide24.limitations.item3": {"message": "Limited processing speed for complex information"}, "ai101.slide24.limitations.item4": {"message": "Difficulty in seeing patterns across multiple perspectives"}, "ai101.slide24.limitations.item5": {"message": "Limited knowledge and perspective"}, "ai101.slide24.partner.title": {"message": "AI as Thinking Partner"}, "ai101.slide24.partner.content": {"message": "Combine human critical thinking with AI processing power"}, "ai101.slide25.comparison.title": {"message": "Linear Conversation vs Multi-Perspective Exploration"}, "ai101.slide25.comparison.linear": {"message": "Linear Conversation"}, "ai101.slide25.comparison.network": {"message": "Multi-Perspective Exploration"}, "ai101.slide25.comparison.linear.item1": {"message": "Single-Direction Conversation"}, "ai101.slide25.comparison.network.item1": {"message": "Multi-Direction Exploration"}, "ai101.slide25.comparison.linear.item2": {"message": "Single Perspective"}, "ai101.slide25.comparison.network.item2": {"message": "Multiple Perspectives"}, "ai101.slide25.comparison.linear.item3": {"message": "Narrower Perspective"}, "ai101.slide25.comparison.network.item3": {"message": "Wider Perspective"}, "ai101.slide25.comparison.linear.item4": {"message": "Quick Answer"}, "ai101.slide25.comparison.network.item4": {"message": "Deep Thinking"}, "ai101.slide25.comparison.linear.item5": {"message": "Focus on Result"}, "ai101.slide25.comparison.network.item5": {"message": "Focus on Process"}, "ai101.slide25.benefits.title": {"message": "Multi-Perspective Thinking Benefits"}, "ai101.slide25.benefits.item1": {"message": "Enhanced creativity through multiple perspectives and connections"}, "ai101.slide25.benefits.item2": {"message": "Better problem-solving capabilities through multiple perspectives"}, "ai101.slide25.benefits.item3": {"message": "Improved learning and retention through critical thinking and visualization"}, "ai101.slide25.benefits.item4": {"message": "More comprehensive understanding through multiple perspectives"}, "ai101.slide25.benefits.item5": {"message": "Support complex problem decomposition, divide and conquer"}, "ai101.slide26.partner.title": {"message": "AI as Partner"}, "ai101.slide26.partner.content": {"message": "Collaborate with AI to expand thinking capabilities"}, "ai101.slide26.analysis.title": {"message": "Enhanced Analysis"}, "ai101.slide26.analysis.content": {"message": "Process and analyze complex information patterns"}, "ai101.slide26.creative.title": {"message": "Creative Catalyst"}, "ai101.slide26.creative.content": {"message": "Generate and explore new ideas and possibilities"}, "ai101.slide26.strategies.title": {"message": "Thinking Enhancement Strategies"}, "ai101.slide26.strategies.item1": {"message": "Use AI for initial idea generation"}, "ai101.slide26.strategies.item2": {"message": "Apply human judgment for refinement"}, "ai101.slide26.strategies.item3": {"message": "Combine multiple perspectives"}, "ai101.slide26.strategies.item4": {"message": "Iterate and improve continuously"}, "ai101.slide26.strategies.item5": {"message": "Maintain critical thinking"}, "ai101.slide26.process.content": {"message": "Combine human critical thinking with AI processing to enhance decision-making"}, "ai101.slide27.transformation.title": {"message": "Educational Transformation"}, "ai101.slide27.transformation.content": {"message": "Adapting to new learning paradigms and opportunities"}, "ai101.slide27.partnership.title": {"message": "Human-AI Partnership"}, "ai101.slide27.partnership.content": {"message": "Leveraging the strengths of both human and artificial intelligence"}, "ai101.slide27.adaptation.title": {"message": "Continuous Adaptation"}, "ai101.slide27.adaptation.content": {"message": "Staying current with evolving AI capabilities and applications"}, "ai101.slide27.takeaways.title": {"message": "Key Takeaways"}, "ai101.slide27.takeaways.item1": {"message": "AI is a tool for enhancing human capabilities"}, "ai101.slide27.takeaways.item2": {"message": "Focus on developing unique human skills"}, "ai101.slide27.takeaways.item3": {"message": "Embrace continuous learning and adaptation"}, "ai101.slide27.takeaways.item4": {"message": "Maintain ethical awareness and responsibility"}, "ai101.slide27.takeaways.item5": {"message": "Build effective human-AI collaboration"}, "ai101.slide27.takeaways.item6": {"message": "Shape the future of education with AI"}, "ai101.slide28.connect.title": {"message": "Connect with FunBlocks AI"}, "ai101.slide28.connect.website.title": {"message": "Website"}, "ai101.slide28.connect.website.content": {"message": "Visit our website for more resources and updates"}, "ai101.slide28.connect.contact.title": {"message": "Contact"}, "ai101.slide28.connect.contact.content": {"message": "Get in touch for collaboration and support"}, "ai101.slide20.continuous.title": {"message": "Continuous Learning"}, "ai101.slide20.continuous.content": {"message": "Learning as an ongoing process throughout life"}, "ai101.slide20.justInTime.title": {"message": "Just-in-Time Learning"}, "ai101.slide20.justInTime.content": {"message": "Learning when and where it's needed"}, "ai101.slide20.collaborative.title": {"message": "Collaborative Learning"}, "ai101.slide20.collaborative.content": {"message": "Learning through human-AI collaboration"}, "ai101.slide20.infrastructure.title": {"message": "Building Learning Infrastructure"}, "ai101.slide20.infrastructure.content": {"message": "Enhancing learning capacity and developing meta-learning skills"}, "ai101.slide21.understanding.title": {"message": "Understanding AI"}, "ai101.slide21.understanding.content": {"message": "Grasp AI capabilities, limitations, and applications"}, "ai101.slide21.ethics.title": {"message": "Ethical Awareness"}, "ai101.slide21.ethics.content": {"message": "Understand ethical implications and responsibilities"}, "ai101.slide21.skills.title": {"message": "Practical Skills"}, "ai101.slide21.skills.content": {"message": "Develop hands-on AI interaction abilities"}, "ai101.slide21.curriculum.title": {"message": "AI Literacy Curriculum"}, "ai101.slide21.curriculum.item1": {"message": "AI fundamentals and concepts"}, "ai101.slide21.curriculum.item2": {"message": "Ethical considerations and implications"}, "ai101.slide21.curriculum.item3": {"message": "Practical AI tools and applications"}, "ai101.slide21.curriculum.item4": {"message": "Critical thinking and evaluation"}, "ai101.slide21.curriculum.item5": {"message": "Future trends and developments"}, "ai101.slide22.conversational.title": {"message": "Conversational AI"}, "ai101.slide22.conversational.content": {"message": "Interactive learning and discussion assistants"}, "ai101.slide22.writing.title": {"message": "Writing Assistants"}, "ai101.slide22.writing.content": {"message": "Tools for content creation and editing"}, "ai101.slide22.creative.title": {"message": "Creative Tools"}, "ai101.slide22.creative.content": {"message": "AI-powered design and multimedia creation"}, "ai101.slide22.data.title": {"message": "Research Tools"}, "ai101.slide22.data.content": {"message": "Information retrieval, summarization, analysis tools"}, "ai101.slide22.learning.title": {"message": "Learning Platforms"}, "ai101.slide22.learning.content": {"message": "AI-enhanced educational platforms"}, "ai101.slide22.thinking.title": {"message": "Thinking Tools"}, "ai101.slide22.thinking.content": {"message": "Tools for enhancing cognitive processes"}, "ai101.slide23.mindMapping.title": {"message": "Critical Thinking"}, "ai101.slide23.mindMapping.content": {"message": "Refine questions, analyze fallacies, and develop critical thinking skills"}, "ai101.slide23.brainstorming.title": {"message": "Creative Thinking"}, "ai101.slide23.brainstorming.content": {"message": "Generate and develop creative ideas with AI"}, "ai101.slide23.exploration.title": {"message": "Boundless Exploration"}, "ai101.slide23.exploration.content": {"message": "Explore endless possibilities with AI on boundless canvas from multiple perspectives"}, "ai101.slide23.structuredThinking.title": {"message": "AI Augmented Thinking"}, "ai101.slide23.structuredThinking.content": {"message": "AI-powered mind mapping and brainstorming"}, "ai101.slide23.mentalModels.title": {"message": "AI-Driven Mental Models Application"}, "ai101.slide23.mentalModels.content": {"message": "Apply classic mental models to complex problems with AI assistance"}, "ai101.slide23.creativeWorkflows.title": {"message": "Creative Workflows"}, "ai101.slide23.creativeWorkflows.content": {"message": "From concept to presentation with integrated AI tools"}, "ai101.slide23.features.title": {"message": "Key Features"}, "ai101.slide23.features.item1": {"message": "AI-powered mind mapping and brainstorming"}, "ai101.slide23.features.item2": {"message": "AI Collaborative thinking spaces"}, "ai101.slide23.features.item3": {"message": "Integration with mainstream AI models"}, "ai101.slide23.features.item4": {"message": "AI-powered Critical Analysis"}, "ai101.slide23.features.item5": {"message": "AI posing questions or providing alternative perspectives"}, "ai101.coreLearning.supervisedLearning.title": {"message": "Supervised Learning"}, "ai101.coreLearning.supervisedLearning.content": {"message": "Learning input-output mappings from labeled data"}, "ai101.coreLearning.supervisedLearning.examples": {"message": "Examples: Image classification, sentiment analysis"}, "ai101.coreLearning.reinforcementLearning.title": {"message": "Reinforcement Learning"}, "ai101.coreLearning.reinforcementLearning.content": {"message": "Optimizing strategies through trial and error with reward signals"}, "ai101.coreLearning.reinforcementLearning.examples": {"message": "Examples: Game AI, robot control"}, "ai101.coreLearning.deepLearning.title": {"message": "Deep Learning"}, "ai101.coreLearning.deepLearning.content": {"message": "Multi-layer neural networks automatically extracting features"}, "ai101.coreLearning.deepLearning.examples": {"message": "End-to-end learning of complex patterns"}, "ai101.coreLearning.powerCombining.title": {"message": "The Power of Combining All Three"}, "ai101.coreLearning.powerCombining.content": {"message": "Supervised Learning lays foundation → Deep Learning extracts features → Reinforcement Learning optimizes behavior"}, "ai101.aiTraining.stage1.title": {"message": "Stage 1: Pre-training"}, "ai101.aiTraining.stage2.title": {"message": "Stage 2: Supervised Fine-tuning"}, "ai101.aiTraining.stage3.title": {"message": "Stage 3: Reinforcement Learning"}, "ai101.aiTraining.process": {"message": "Raw Text → Language Model → Instruction Follower → Human-Aligned AI"}, "ai101.neuralNetwork.biologicalNeurons.title": {"message": "Biological Neurons"}, "ai101.neuralNetwork.artificialNeurons.title": {"message": "Artificial Neurons"}, "ai101.neuralNetwork.deepNetworks.title": {"message": "Deep Networks"}, "ai101.neuralNetwork.architecture.title": {"message": "Network Architecture"}, "ai101.neuralNetwork.architecture.content": {"message": "Input Layer → Hidden Layers → Output Layer\n    ↓                ↓                ↓\n  Raw Data → Feature Extraction → Predictions"}, "ai101.math.probabilityTheory.title": {"message": "Probability Theory"}, "ai101.math.statistics.title": {"message": "Statistics"}, "ai101.math.optimization.title": {"message": "Optimization"}, "ai101.math.coreConcepts.title": {"message": "Core Mathematical Concepts"}, "ai101.math.coreConcepts.item1": {"message": "Linear Algebra: Vector spaces and transformations"}, "ai101.math.coreConcepts.item2": {"message": "Calculus: Gradient descent and backpropagation"}, "ai101.math.coreConcepts.item3": {"message": "Information Theory: Entropy and compression"}, "ai101.math.coreConcepts.item4": {"message": "Graph Theory: Network structures and relationships"}, "ai101.keyConcepts.parameters.title": {"message": "Parameters"}, "ai101.keyConcepts.tokens.title": {"message": "Tokens"}, "ai101.keyConcepts.attention.title": {"message": "Attention"}, "ai101.keyConcepts.scaleComparison.title": {"message": "Scale Comparison"}, "ai101.keyConcepts.table.model": {"message": "Model"}, "ai101.keyConcepts.table.parameters": {"message": "Parameters"}, "ai101.keyConcepts.table.trainingData": {"message": "Training Data"}, "ai101.keyConcepts.table.humanBrain": {"message": "Human Brain"}, "ai101.keyConcepts.table.lifetimeExperience": {"message": "Lifetime experience"}, "ai101.intelligence.informationCompression.title": {"message": "Information Compression"}, "ai101.intelligence.patternRecognition.title": {"message": "Pattern Recognition"}, "ai101.intelligence.asCompression.title": {"message": "Intelligence as Compression"}, "ai101.intelligence.asCompression.content": {"message": "Raw Data (Terabytes) → Compressed Knowledge (Gigabytes) → Predictions\n\nExample: Learning language from billions of words\n→ Compressed into grammar rules and patterns\n→ Generate coherent new sentences"}, "ai101.scaling.moreParameters.title": {"message": "More Parameters"}, "ai101.scaling.moreData.title": {"message": "More Data"}, "ai101.scaling.moreCompute.title": {"message": "More Compute"}, "ai101.scaling.trends.title": {"message": "Scaling Trends"}, "ai101.scaling.trends.item1": {"message": "Performance improves predictably with scale"}, "ai101.scaling.trends.item2": {"message": "Emergent abilities appear at certain thresholds"}, "ai101.scaling.trends.item3": {"message": "But scaling has physical and economic limits"}, "ai101.scaling.trends.item4": {"message": "Efficiency improvements become crucial"}, "ai101.understanding.statisticalMastery.title": {"message": "Statistical Mastery"}, "ai101.understanding.semanticUnderstanding.title": {"message": "Semantic Understanding?"}, "ai101.understanding.chineseRoom.title": {"message": "The Chinese Room Argument"}, "ai101.understanding.chineseRoom.content": {"message": "Person in room follows rules to respond to Chinese characters\n↓\nAppears to understand Chinese, but actually doesn't\n↓\nSimilarly, AI might simulate understanding without true comprehension"}, "ai101.understanding.currentEvidence.title": {"message": "Current Evidence"}, "ai101.understanding.currentEvidence.item1": {"message": "AI shows remarkable language capabilities"}, "ai101.understanding.currentEvidence.item2": {"message": "Can reason about abstract concepts"}, "ai101.understanding.currentEvidence.item3": {"message": "But lacks grounded experience in the world"}, "ai101.understanding.currentEvidence.item4": {"message": "Understanding vs. sophisticated pattern matching remains debated"}, "ai101.agi.currentState.title": {"message": "Current State"}, "ai101.agi.agiVision.title": {"message": "AGI Vision"}, "ai101.agi.vsHuman.title": {"message": "AI vs Human Capabilities"}, "ai101.agi.table.domain": {"message": "Domain"}, "ai101.agi.table.aiStatus": {"message": "AI Status"}, "ai101.agi.table.humanLevel": {"message": "Human Level"}, "ai101.agi.table.surpassed": {"message": "Surpassed"}, "ai101.agi.table.matched": {"message": "Matched"}, "ai101.agi.table.nearHuman": {"message": "Near human"}, "ai101.agi.table.belowHuman": {"message": "Below human"}, "ai101.agi.table.debated": {"message": "Debated"}, "ai101.threats.jobDisplacement.title": {"message": "Job Displacement"}, "ai101.threats.misinformation.title": {"message": "Misinformation"}, "ai101.threats.biasAmplification.title": {"message": "<PERSON><PERSON> Amplification"}, "ai101.threats.mitigation.title": {"message": "Mitigation Strategies"}, "ai101.threats.mitigation.item1": {"message": "Develop AI governance and regulation frameworks"}, "ai101.threats.mitigation.item2": {"message": "Invest in education and reskilling programs"}, "ai101.threats.mitigation.item3": {"message": "Promote responsible AI development practices"}, "ai101.threats.mitigation.item4": {"message": "Foster human-AI collaboration rather than replacement"}, "ai101.survival.continuousLearning.title": {"message": "Continuous Learning"}, "ai101.survival.aiCollaboration.title": {"message": "AI Collaboration"}, "ai101.survival.humanUniqueness.title": {"message": "Human Uniqueness"}, "ai101.survival.strategicApproach.title": {"message": "Strategic Approach"}, "ai101.survival.strategicApproach.content": {"message": "Short-term: Learn AI tools and workflows\nMedium-term: Develop AI-human collaboration skills\nLong-term: Focus on creativity, empathy, and complex reasoning"}, "ai101.agi.table.chessGo": {"message": "Chess/Go"}, "ai101.agi.table.imageRecognition": {"message": "Image Recognition"}, "ai101.agi.table.languageTasks": {"message": "Language Tasks"}, "ai101.agi.table.generalReasoning": {"message": "General Reasoning"}, "ai101.agi.table.creativity": {"message": "Creativity"}, "ai101.agi.table.superhuman": {"message": "✅ Superhuman"}, "ai101.agi.table.humanLevelStatus": {"message": "✅ Human-level"}, "ai101.agi.table.approaching": {"message": "🔄 Approaching"}, "ai101.agi.table.uncertain": {"message": "❓ Uncertain"}, "ai101.agi.table.emerging": {"message": "🎨 Emerging"}, "ai101.timeline.1950": {"message": "<PERSON><PERSON> proposed the Turing Test"}, "ai101.timeline.1956": {"message": "Dartmouth Conference, birth of AI concept"}, "ai101.timeline.1980s": {"message": "Rise of expert systems and AI winter"}, "ai101.timeline.2000s": {"message": "Machine learning renaissance"}, "ai101.timeline.2012": {"message": "Deep learning breakthrough (AlexNet)"}, "ai101.timeline.2017": {"message": "Transformer architecture emerged"}, "ai101.timeline.2022": {"message": "ChatGPT sparked generative AI revolution"}, "ai101.whyNow.computing.title": {"message": "Computing Power Breakthrough"}, "ai101.whyNow.computing.item1": {"message": "GPU parallel computing revolution"}, "ai101.whyNow.computing.item2": {"message": "Cloud computing lowering barriers"}, "ai101.whyNow.computing.item3": {"message": "Specialized AI chips emerging"}, "ai101.whyNow.data.title": {"message": "Data Explosion"}, "ai101.whyNow.data.item1": {"message": "Internet content exponential growth"}, "ai101.whyNow.data.item2": {"message": "Accelerated digitization process"}, "ai101.whyNow.data.item3": {"message": "Mature data annotation techniques"}, "ai101.whyNow.technical.title": {"message": "Technical Architecture"}, "ai101.whyNow.technical.item1": {"message": "Transformer revolutionary breakthrough"}, "ai101.whyNow.technical.item2": {"message": "Attention mechanism innovation"}, "ai101.whyNow.technical.item3": {"message": "End-to-end learning paradigm"}, "ai101.concept.hierarchy": {"message": "AGI (Artificial General Intelligence)\n    ↑ Target direction\nGenAI (Generative AI)\n    ↑ Current stage\nLLM (Large Language Model)\n    ↑ Core technology"}, "ai101.concept.llm.title": {"message": "LLM"}, "ai101.concept.llm.content": {"message": "Large-scale language models based on Transformer architecture"}, "ai101.concept.genai.title": {"message": "GenAI"}, "ai101.concept.genai.content": {"message": "AI systems capable of generating text, images, audio and other content"}, "ai101.concept.agi.title": {"message": "AGI"}, "ai101.concept.agi.content": {"message": "General intelligence that matches or exceeds humans in all cognitive tasks"}, "ai101.genai.differences.traditional": {"message": "Traditional AI"}, "ai101.genai.differences.generative": {"message": "Generative AI"}, "ai101.genai.differences.traditional.item1": {"message": "Recognition & Classification"}, "ai101.genai.differences.generative.item1": {"message": "Content Creation"}, "ai101.genai.differences.traditional.item2": {"message": "Rule-driven"}, "ai101.genai.differences.generative.item2": {"message": "Data-driven"}, "ai101.genai.differences.traditional.item3": {"message": "Specialized Systems"}, "ai101.genai.differences.generative.item3": {"message": "General Capabilities"}, "ai101.genai.differences.traditional.item4": {"message": "Deterministic Output"}, "ai101.genai.differences.generative.item4": {"message": "Probabilistic Generation"}, "ai101.genai.coreRelatedTech.title": {"message": "Core Related Technologies"}, "ai101.genai.coreRelatedTech.item1": {"message": "Deep Neural Networks"}, "ai101.genai.coreRelatedTech.item2": {"message": "Attention Mechanisms"}, "ai101.genai.coreRelatedTech.item3": {"message": "Pre-training & Fine-tuning Paradigm"}, "ai101.genai.coreRelatedTech.item4": {"message": "Reinforcement Learning Alignment"}, "ai101.generativeEra.title": {"message": "Learning in the Generative AI Era"}, "ai101.generativeEra.subtitle": {"message": "Transforming Education with AI"}, "ai101.generativeEra.personalized.title": {"message": "Personalized Learning"}, "ai101.generativeEra.personalized.content": {"message": "AI adapts to individual learning styles and pace"}, "ai101.generativeEra.interactive.title": {"message": "Interactive Content"}, "ai101.generativeEra.interactive.content": {"message": "Dynamic and engaging learning materials"}, "ai101.generativeEra.assessment.title": {"message": "Smart Assessment"}, "ai101.generativeEra.assessment.content": {"message": "Real-time feedback and progress tracking"}, "ai101.generativeEra.collaboration.title": {"message": "Enhanced Collaboration"}, "ai101.generativeEra.collaboration.content": {"message": "AI-facilitated group learning and discussion"}, "ai101.gpt.title": {"message": "GPT (Generative Pre-trained Transformer)"}, "ai101.gpt.subtitle": {"message": "Understanding the Core Technology of Modern AI"}, "ai101.gpt.definition.title": {"message": "Core Definition"}, "ai101.gpt.definition.item1": {"message": "Generative Pre-trained Transformer: Large language model based on Transformer architecture"}, "ai101.gpt.definition.item2": {"message": "Autoregressive language model: Generates text by predicting the next word"}, "ai101.gpt.definition.item3": {"message": "Unsupervised pre-training + Supervised fine-tuning: Typical representative of two-stage training paradigm"}, "ai101.gpt.features.title": {"message": "Key Features"}, "ai101.gpt.features.item1": {"message": "Unidirectional attention mechanism: Can only see previous text, suitable for text generation tasks"}, "ai101.gpt.features.item2": {"message": "Large-scale parameters: From GPT-1's 117 million to GPT-4's hundreds of billions of parameters"}, "ai101.gpt.features.item3": {"message": "Powerful zero-shot and few-shot learning capabilities"}, "ai101.gptArchitecture.title": {"message": "GPT Core Technical Architecture"}, "ai101.gptArchitecture.subtitle": {"message": "Deep Understanding of GPT's Technical Foundation"}, "ai101.gptArchitecture.transformer.title": {"message": "Transformer Decoder"}, "ai101.gptArchitecture.transformer.item1": {"message": "Multi-head self-attention: Captures long-range dependencies"}, "ai101.gptArchitecture.transformer.item2": {"message": "Positional encoding: Understands positional information in text sequences"}, "ai101.gptArchitecture.transformer.item3": {"message": "Residual connections + Layer normalization: Stabilizes training process"}, "ai101.gptArchitecture.pretraining.title": {"message": "Pre-training Strategy"}, "ai101.gptArchitecture.pretraining.item1": {"message": "Next word prediction: Learning language patterns from large-scale text corpora"}, "ai101.gptArchitecture.pretraining.item2": {"message": "Causal masking: Ensures access only to previous words"}, "ai101.gptArchitecture.pretraining.item3": {"message": "Large-scale data: Diverse data sources including internet text, books, news"}, "ai101.gptArchitecture.alignment.title": {"message": "Fine-tuning and Alignment"}, "ai101.gptArchitecture.alignment.item1": {"message": "Instruction fine-tuning: Improves model's ability to follow instructions"}, "ai101.gptArchitecture.alignment.item2": {"message": "Reinforcement Learning from Human Feedback (RLHF): Aligns model outputs with human preferences"}, "ai101.gptArchitecture.alignment.item3": {"message": "Safety filtering: Reduces generation of harmful content"}, "ai101.gptCapabilities.title": {"message": "GPT Core Capabilities and Applications"}, "ai101.gptCapabilities.subtitle": {"message": "From Text Generation to Intelligent Reasoning"}, "ai101.gptCapabilities.textGeneration.title": {"message": "Text Generation Capabilities"}, "ai101.gptCapabilities.textGeneration.item1": {"message": "Creative writing: Stories, poetry, script creation"}, "ai101.gptCapabilities.textGeneration.item2": {"message": "Technical documentation: API docs, user manuals, technical reports"}, "ai101.gptCapabilities.textGeneration.item3": {"message": "Marketing content: Ad copy, product descriptions, social media content"}, "ai101.gptCapabilities.understanding.title": {"message": "Understanding and Reasoning"}, "ai101.gptCapabilities.understanding.item1": {"message": "Reading comprehension: Answering complex text-based questions"}, "ai101.gptCapabilities.understanding.item2": {"message": "Logical reasoning: Solving mathematical problems and logic puzzles"}, "ai101.gptCapabilities.understanding.item3": {"message": "Knowledge Q&A: Cross-domain encyclopedic knowledge queries"}, "ai101.gptCapabilities.coding.title": {"message": "Code Generation"}, "ai101.gptCapabilities.coding.item1": {"message": "Program writing: Generating code based on requirements"}, "ai101.gptCapabilities.coding.item2": {"message": "Code explanation: Understanding and commenting existing code"}, "ai101.gptCapabilities.coding.item3": {"message": "Debugging assistance: Finding and fixing code errors"}, "ai101.gptLimitations.title": {"message": "GPT Technical Advantages and Limitations"}, "ai101.gptLimitations.subtitle": {"message": "Rational Understanding of GPT's Capability Boundaries"}, "ai101.gptLimitations.advantages.title": {"message": "Main Advantages"}, "ai101.gptLimitations.advantages.item1": {"message": "Strong generalization: One model handles multiple tasks"}, "ai101.gptLimitations.advantages.item2": {"message": "In-context learning: Quickly adapts to new tasks through examples"}, "ai101.gptLimitations.advantages.item3": {"message": "Creative output: Generates novel and useful content"}, "ai101.gptLimitations.limitations.title": {"message": "Current Limitations"}, "ai101.gptLimitations.limitations.item1": {"message": "Hallucination issues: May generate seemingly reasonable but actually incorrect information"}, "ai101.gptLimitations.limitations.item2": {"message": "Knowledge cutoff: Training data has time limitations"}, "ai101.gptLimitations.limitations.item3": {"message": "High computational cost: Inference requires significant computational resources"}, "ai101.gptLimitations.limitations.item4": {"message": "Poor interpretability: Difficult to understand model's decision-making process"}, "ai101.conceptsExplained.title": {"message": "Parameters, Dimensions, and Tokens Explained"}, "ai101.conceptsExplained.subtitle": {"message": "Understanding Core Concepts of Large Language Models"}, "ai101.conceptsExplained.token.title": {"message": "Token"}, "ai101.conceptsExplained.token.definition": {"message": "Definition: Token is the smallest unit for model text processing, can be a single character, word, or even part of a word. Created by a tokenizer that splits raw text."}, "ai101.conceptsExplained.token.examples": {"message": "Examples:\nEnglish: \"ChatGPT is great\" might be split into: [\"Chat\", \"G\", \"PT\", \" is\", \" great\"]\nChinese: \"大模型很好用\" might be split into: [\"大\", \"模型\", \"很\", \"好\", \"用\"]"}, "ai101.conceptsExplained.token.analogy": {"message": "Analogy: If you think of a sentence as a wall built with blocks, tokens are each individual block."}, "ai101.conceptsExplained.dimensions.title": {"message": "Dimensions"}, "ai101.conceptsExplained.dimensions.definition": {"message": "Definition: Dimension is the position or \"length\" of each data point in a vector or matrix. Commonly used to describe the vector space size in hidden layers."}, "ai101.conceptsExplained.dimensions.usage": {"message": "Common usage:\nWord vector dimensions (embedding size): e.g., a word mapped to a 768-dimensional vector.\nHidden layer dimensions (hidden size): represents the vector length of each neuron's output in each layer."}, "ai101.conceptsExplained.dimensions.analogy": {"message": "Analogy: If a token is a product, dimensions are the number of \"feature labels\" it has, like color, size, purpose, etc."}, "ai101.conceptsExplained.parameters.title": {"message": "Parameters"}, "ai101.conceptsExplained.parameters.definition": {"message": "Definition: Parameters are the \"knowledge\" learned by the model during training. They include weights and biases in neural networks."}, "ai101.conceptsExplained.parameters.scale": {"message": "Scale: GPT-3 has 175 billion parameters, GPT-4 is estimated to have even more."}, "ai101.conceptsExplained.parameters.analogy": {"message": "Analogy: Think of the model as a brain, parameters are the \"memory connections\" or \"experiences\" formed in that brain."}, "ai101.conceptsExplained.comparison.title": {"message": "Summary Comparison"}, "homepage.head.title": {"message": "All-in-One AI Workspace – From Mind Maps to Slides & Docs"}, "homepage.head.description": {"message": "Supercharge learning, writing, and creativity with FunBlocks AI! Go beyond ChatGPT with powerful AI-powered mind maps that visualize complex ideas. Instantly transform brainstorms into slides, infographics, and documents. Boost productivity in an all-in-one workspace supporting GPT, Claude & Gemini. Unlock your cognitive potential today!"}, "homepage.nav.home": {"message": "FunBlocks AI"}, "homepage.nav.aiflow": {"message": "AIFlow"}, "homepage.nav.tools": {"message": "Quick Start"}, "homepage.nav.useCases": {"message": "Use Cases"}, "homepage.nav.workspace": {"message": "Workspace"}, "homepage.nav.pricing": {"message": "Pricing"}, "homepage.nav.trial": {"message": "Free Trial"}, "homepage.hero.title": {"message": "Explore, Think and Create with AI"}, "homepage.hero.subtitle": {"message": "An AI-powered platform that revolutionizes thinking and boosts productivity. Generate ideas with AI, explore topics through mindmaps, and transform them into actionable strategies, engaging presentations, and insightful infographics."}, "homepage.hero.trial": {"message": "Free Trial Now"}, "homepage.hero.learnMore": {"message": "Learn More"}, "homepage.beyond.title": {"message": "Beyond ChatGPT"}, "homepage.beyond.description": {"message": "Text chatbox is not the only way to interact with AI. Visualize your thoughts and AI's output in formats that truly enhance your cognitive process."}, "homepage.beyond.visualize.title": {"message": "Clarify Complex Thinking through Mind Maps"}, "homepage.beyond.visualize.description": {"message": "Transform linear conversations into multidimensional mind maps that reveal connections, patterns, and insights that text alone cannot convey."}, "homepage.beyond.accelerate.title": {"message": "Accelerate Creativity and Innovation"}, "homepage.beyond.accelerate.description": {"message": "Combine AI's vast knowledge with visual thinking techniques to break through creative barriers and generate breakthrough ideas."}, "homepage.beyond.streamline.title": {"message": "Boost Productivity with an All-in-One Workflow"}, "homepage.beyond.streamline.description": {"message": "Transform complex information into structured visual outputs that can be instantly converted into professional deliverables in your workflow."}, "homepage.features.visualize.title": {"message": "Clarify Complex Thinking through Mind Maps"}, "homepage.features.visualize.description": {"message": "Traditional AI chatbots limit thinking to a linear conversation. FunBlocks AIFlow expands your cognitive horizon with multidimensional mind maps on an infinite canvas."}, "homepage.features.visualize.point1": {"message": "Explore any topic from multiple angles simultaneously, revealing insights that linear thinking misses"}, "homepage.features.visualize.point2": {"message": "Dive infinitely deeper into any subtopic while maintaining the full context, creating comprehensive knowledge structures"}, "homepage.features.visualize.point3": {"message": "Let AI generate an entire exploration space with related topics and questions you hadn't considered"}, "homepage.features.visualize.point4": {"message": "Refine your thinking with AI-optimized questions that target the core of complex problems"}, "homepage.features.accelerate.title": {"message": "Accelerate Creativity and Innovation"}, "homepage.features.accelerate.description": {"message": "Move beyond the creative limitations of text-based AI interactions. FunBlocks AIFlow combines AI insights with powerful visual frameworks to spark genuine innovation."}, "homepage.features.accelerate.point1": {"message": "Integrate AI's vast knowledge with visual brainstorming to generate connections human minds might not naturally make"}, "homepage.features.accelerate.point2": {"message": "Deconstruct complex problems visually to identify root causes and hidden opportunities"}, "homepage.features.accelerate.point3": {"message": "Apply classic thinking models (like SCAMPER, Six Thinking Hats, and First Principles) directly in your mind maps with AI assistance"}, "homepage.features.accelerate.point4": {"message": "Overcome creative blocks by visually expanding idea spaces in multiple directions simultaneously"}, "homepage.features.streamline.title": {"message": "Boost Productivity with an All-in-One Workflow"}, "homepage.features.streamline.description": {"message": "Transform the way you work with information. FunBlocks AIFlow turns complex knowledge work into a visual, collaborative process with immediate, tangible outputs."}, "homepage.features.streamline.point1": {"message": "Instantly break down overwhelming projects into manageable visual components with AI assistance"}, "homepage.features.streamline.point2": {"message": "Interact with AI through an intuitive visual interface that matches how your brain naturally processes complex information"}, "homepage.features.streamline.point3": {"message": "Collaborate with AI to explore multiple solution paths simultaneously, identifying the optimal approach faster"}, "homepage.features.streamline.point4": {"message": "Convert your visual thinking directly into professional deliverables—slides, infographics, documents—with a single click"}, "homepage.thinking_matters.title": {"message": "Your Thinking Matters in the Age of AI", "description": "Title for the Thinking Matters section"}, "homepage.thinking_matters.description": {"message": "In today's fast-paced world filled with AI tools, effective thinking skills are more valuable than ever. FunBlocks empowers you to enhance your cognitive abilities through structured thinking methods and visualization.", "description": "Description for the Thinking Matters section"}, "homepage.thinking_matters.master.title": {"message": "Master Your Mind", "description": "Title for the Master Your Mind card"}, "homepage.thinking_matters.master.description": {"message": "In today's information-rich world, effective thinking methods are essential. FunBlocks AI helps you cut through noise, gain clarity, and boost efficiency by integrating proven cognitive techniques with intuitive technology.", "description": "Description for the Master Your Mind card"}, "homepage.thinking_matters.concepts.title": {"message": "Core Thinking Frameworks", "description": "Title for the Core Thinking Concepts card"}, "homepage.thinking_matters.concepts.description": {"message": "Our platform is built on fundamental thinking methodologies and tools: Brainstorming for idea generation, Mind Mapping for organization, Critical Thinking for analysis, Creative Thinking for innovation, and Mental Models for understanding and action.", "description": "Description for the Core Thinking Concepts card"}, "homepage.thinking_matters.empowerment.title": {"message": "AI-Enhanced Thinking", "description": "Title for the AI-Enhanced Thinking card"}, "homepage.thinking_matters.empowerment.description": {"message": "FunBlocks AI enhances your thinking with a unified workspace, visualization tools, AI assistance, and seamless collaboration features—all designed to help you think more deeply and effectively.", "description": "Description for the AI-Enhanced Thinking card"}, "homepage.thinking_matters.learn_more": {"message": "Learn More About Our Thinking Toolkit", "description": "Text for the Learn More button"}, "homepage.tools.title": {"message": "AI Tools Tailored for Diverse Use Cases"}, "homepage.tools.description": {"message": "Specifically designed for various scenarios, enabling users to leverage AI effortlessly and lowering the learning curve for enhanced creativity and productivity."}, "homepage.tools.tools_list": {"message": "AI Tools"}, "homepage.tools.tool1.title": {"message": "AI Mind Map Generator"}, "homepage.tools.tool1.description": {"message": "Easily convert documents, books, and movies into mind maps to help you clarify complex ideas quickly."}, "homepage.tools.tool2.title": {"message": "AI Brainstorming"}, "homepage.tools.tool2.description": {"message": "Combine AI with classic thinking models to brainstorm on specific topics or problems, sparking creativity and innovative solutions."}, "homepage.tools.tool3.title": {"message": "AI Critical Thinking"}, "homepage.tools.tool3.description": {"message": "Enhance your critical thinking skills, gaining insights from multiple perspectives through questioning, analysis, and reflection."}, "homepage.tools.tool4.title": {"message": "AI Slides Generator"}, "homepage.tools.tool4.description": {"message": "Quickly create professional slides and presentations on any topic, making it easy to prepare for your presentations."}, "homepage.tools.tool5.title": {"message": "AI Infographics Generator"}, "homepage.tools.tool5.description": {"message": "Automatically generate infographics and knowledge cards from input text, helping you convey information visually."}, "homepage.tools.tool6.title": {"message": "AI Art Insight"}, "homepage.tools.tool6.description": {"message": "<PERSON>ain deeper artistic analysis and appreciation by taking photos during your travels and museum visits."}, "homepage.tools.tool7.title": {"message": "AI Education Tool"}, "homepage.tools.tool7.description": {"message": "Break down topics into progressive cognitive levels based on <PERSON>'s educational theories to enhance learning efficiency and teaching effectiveness."}, "homepage.tools.tool8.title": {"message": "AI Psychological Insights"}, "homepage.tools.tool8.description": {"message": "Receive psychological counseling and dream interpretation services anytime, helping you better understand your inner world."}, "homepage.tools.tool9.title": {"message": "AI Image Generator"}, "homepage.tools.tool9.description": {"message": "Generate personalized avatars and images in your desired style with a single click."}, "homepage.multiModel.title": {"message": "All Leading AI Models in One Place"}, "homepage.multiModel.description": {"message": "Access all mainstream large language models through a single FunBlocks AI subscription. No need to manage multiple subscriptions or switch between platforms."}, "homepage.multiModel.advantage1.title": {"message": "One Subscription, All Models"}, "homepage.multiModel.advantage1.description": {"message": "Access all leading AI models through a single FunBlocks subscription. No need to manage multiple accounts or pay separate fees."}, "homepage.multiModel.advantage2.title": {"message": "Significant Cost Savings"}, "homepage.multiModel.advantage2.description": {"message": "Save up to 70% compared to subscribing to each AI platform separately. One subscription unlocks all premium model capabilities."}, "homepage.multiModel.advantage3.title": {"message": "Complete Product Access"}, "homepage.multiModel.advantage3.description": {"message": "Your subscription gives you full access to all FunBlocks AI products - AIFlow, AI Docs, AI Slides, and more. Only the number of AI requests varies by plan."}, "homepage.multiModel.benefit": {"message": "Experience the power of multiple AI models without the complexity and cost of managing multiple subscriptions."}, "homepage.workspace.title": {"message": "FunBlocks: All-in-one AI Workspace"}, "homepage.workspace.description": {"message": "Beyond AIFlow, FunBlocks serves as an all-in-one AI workspace designed to meet all your work and learning requirements, offering seamlessly integrated tools for writing, presenting, brainstorming, browsing and research."}, "homepage.workspace.writer.title": {"message": "AI Docs"}, "homepage.workspace.writer.description": {"message": "Experience Notion-style Block Editor with AI-powered writing assistance. Create beautiful documents, notes, and content with intelligent suggestions and formatting."}, "homepage.workspace.slides.title": {"message": "AI Slides"}, "homepage.workspace.slides.description": {"message": "Generate professional presentations with a single click based on any topic. Fully supports Markdown formatting for easy writing and editing of slide content."}, "homepage.workspace.aiflow.title": {"message": "AIFlow"}, "homepage.workspace.aiflow.description": {"message": "Unleash creativity with AI-powered whiteboard and mind mapping tools. Visualize complex ideas, brainstorm with AI assistance, and organize your thoughts effectively."}, "homepage.workspace.extension.title": {"message": "AI Browser Extension"}, "homepage.workspace.extension.description": {"message": "Enhance your browsing experience with an intelligent extension that assists with reading and writing on any webpage. Summarize content, draft responses, and research efficiently."}, "homepage.useCases.title": {"message": "Use Cases"}, "homepage.useCases.description": {"message": "FunBlocks AIFlow adapts to diverse knowledge work scenarios, enhancing your thinking process from exploration to execution."}, "homepage.useCases.learning.title": {"message": "Topic-Based Learning"}, "homepage.useCases.learning.description": {"message": "Build comprehensive knowledge systems and discover cross-disciplinary connections. Transform books, lectures, and resources into interactive visual mind maps."}, "homepage.useCases.strategy.title": {"message": "Work Plan Strategy"}, "homepage.useCases.strategy.description": {"message": "Develop strategic work plans by visually mapping goals, breaking down complex initiatives, and generating solution paths with AI guidance."}, "homepage.useCases.analysis.title": {"message": "Content Analysis"}, "homepage.useCases.analysis.description": {"message": "Deeply analyze films, books, and artworks through mind mapping, extracting profound insights and constructing a comprehensive knowledge system and framework of thought."}, "homepage.useCases.problem.title": {"message": "Problem Analysis"}, "homepage.useCases.problem.description": {"message": "Visualize complex problems from multiple angles, identify root causes, and discover innovative solutions through structured AI-assisted exploration."}, "homepage.useCases.content.title": {"message": "Content Generation"}, "homepage.useCases.content.description": {"message": "Transform brainstorming mind maps into polished documents, professional slides, compelling images, and informative infographics with a single click, streamlining your workflow."}, "homepage.useCases.thinking.title": {"message": "Thinking Enhancement"}, "homepage.useCases.thinking.description": {"message": "Progressively improve your critical and creative thinking abilities through AI-guided mind mapping. Develop advanced cognitive skills through regular use of structured thinking frameworks."}, "homepage.testimonials.title": {"message": "What Our Users Say"}, "homepage.testimonials.description": {"message": "Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."}, "homepage.testimonials.user1.name": {"message": "<PERSON>"}, "homepage.testimonials.user1.role": {"message": "Education Consultant"}, "homepage.testimonials.user1.text": {"message": "\"FunBlocks AI has revolutionized how I create educational content. The mind mapping features help me organize complex topics visually, and being able to convert those maps directly into presentation slides saves me hours of work. My students love the clear, visually engaging materials I now produce.\""}, "homepage.testimonials.user2.name": {"message": "<PERSON>"}, "homepage.testimonials.user2.role": {"message": "Product Manager"}, "homepage.testimonials.user2.text": {"message": "\"I've tried numerous productivity tools, but FunBlocks AI stands out. The combination of AI-powered mind mapping and the ability to generate professional documents in seconds has dramatically improved our team's workflow. The most impressive part? I've noticed my own critical thinking skills improving with regular use.\""}, "prompt_optimizer.head.title": {"message": "FunBlocks AI Prompt Optimizer & Critical Response Analyzer - Enhance AI Conversations"}, "prompt_optimizer.head.description": {"message": "Optimize your prompts and enhance critical thinking with our AI assistant for ChatGPT, <PERSON>, <PERSON>, and more. Features prompt optimization, critical analysis, related questions, and topic exploration to help you think better with AI."}, "prompt_optimizer.head.keywords": {"message": "AI prompt optimizer, critical thinking assistant, ChatGPT prompts, <PERSON> prompts, AI conversation enhancement, prompt engineering, critical analysis, AI assistant, browser extension, better AI answers"}, "prompt_optimizer.hero.badge": {"message": "NEW BROWSER EXTENSION"}, "prompt_optimizer.hero.title": {"message": "AI Prompt Optimizer & Response Analyzer"}, "prompt_optimizer.hero.subtitle": {"message": "Enhance your AI conversations with prompt optimization and critical thinking tools. Let AI help you think better, not replace your thinking."}, "prompt_optimizer.hero.trial": {"message": "Download Extension for FREE"}, "prompt_optimizer.hero.learn_more": {"message": "See How It Works"}, "prompt_optimizer.hero.stat1": {"message": "Free Optimizations"}, "prompt_optimizer.hero.stat2": {"message": "Supported AI Platforms"}, "prompt_optimizer.hero.stat3": {"message": "User Rating"}, "prompt_optimizer.hero.image_caption": {"message": "Click to enlarge"}, "prompt_optimizer.benefits.title": {"message": "Key Benefits"}, "prompt_optimizer.benefits.description": {"message": "Discover how FunBlocks AI Prompt Optimizer transforms your AI interactions with these powerful benefits:"}, "prompt_optimizer.benefits.benefit1.title": {"message": "More Accurate Responses"}, "prompt_optimizer.benefits.benefit1.description": {"message": "Get precisely what you need from AI with optimized prompts that clearly communicate your intent and requirements."}, "prompt_optimizer.benefits.benefit2.title": {"message": "Save Time & Effort"}, "prompt_optimizer.benefits.benefit2.description": {"message": "Eliminate back-and-forth clarifications by starting with well-crafted prompts that address all necessary details."}, "prompt_optimizer.benefits.benefit3.title": {"message": "Improve Your Prompting Skills"}, "prompt_optimizer.benefits.benefit3.description": {"message": "Learn by example as you see how the extension transforms basic prompts into powerful instructions."}, "prompt_optimizer.benefits.benefit4.title": {"message": "Critical Thinking Enhancement"}, "prompt_optimizer.benefits.benefit4.description": {"message": "Develop critical thinking skills with AI-powered analysis tools that help you evaluate responses and think more deeply."}, "prompt_optimizer.benefits.benefit7.title": {"message": "Deeper Exploration"}, "prompt_optimizer.benefits.benefit7.description": {"message": "Discover related questions and topics to explore subjects more thoroughly and from different perspectives."}, "prompt_optimizer.benefits.benefit5.title": {"message": "Works With Your Favorite AI Tools"}, "prompt_optimizer.benefits.benefit5.description": {"message": "Compatible with ChatGPT, Claude, Gemini, Perplexity, DeepSeek and other popular AI chat applications."}, "prompt_optimizer.benefits.benefit6.title": {"message": "One-Click Implementation"}, "prompt_optimizer.benefits.benefit6.description": {"message": "Seamlessly replace your original prompt with the optimized version with just a single click. AI-powered prompt engineering at your fingertips."}, "prompt_optimizer.how_it_works.title": {"message": "How the Prompt Optimizer Works"}, "prompt_optimizer.how_it_works.description": {"message": "Our browser extension seamlessly integrates with leading AI platforms like ChatGPT, Claude, and Gemini, providing powerful prompt optimization and critical thinking tools right where you need them"}, "prompt_optimizer.how_it_works.step1.title": {"message": "Install the Extension"}, "prompt_optimizer.how_it_works.step1.description": {"message": "Add the Prompt Optimizer to Chrome or Edge in just a few clicks. No complex setup required."}, "prompt_optimizer.how_it_works.step2.title": {"message": "Visit Your Favorite AI Chat"}, "prompt_optimizer.how_it_works.step2.description": {"message": "Open ChatGPT, <PERSON>, <PERSON>, or any supported AI platform and see our tools automatically appear."}, "prompt_optimizer.how_it_works.step3.title": {"message": "Optimize Your Prompts"}, "prompt_optimizer.how_it_works.step3.description": {"message": "Use our one-click tools to transform basic questions into powerful, precise prompts that get better results."}, "prompt_optimizer.how_it_works.feature1.badge": {"message": "FEATURE HIGHLIGHT"}, "prompt_optimizer.how_it_works.feature1.title": {"message": "Refine Question & Optimize Prompt"}, "prompt_optimizer.how_it_works.feature1.description": {"message": "The extension adds a Prompt Optimizer widget below the input box in AI chat applications. Our AI analyzes your prompts and suggests improvements based on proven prompt engineering techniques."}, "prompt_optimizer.how_it_works.feature1.point1": {"message": "Choose \"Optimize Question\" to generate 5 more accurate, specific, or different perspectives on your question"}, "prompt_optimizer.how_it_works.feature1.point2": {"message": "Select \"Optimize Instruction\" to clarify your prompt based on your intent and core needs"}, "prompt_optimizer.how_it_works.feature1.point3": {"message": "If your instruction needs more information, a dynamic form will appear to help you provide the necessary details"}, "prompt_optimizer.how_it_works.feature1.point4": {"message": "Replace your original prompt with the optimized version with a single click"}, "prompt_optimizer.how_it_works.feature1.image_caption": {"message": "Click to enlarge"}, "prompt_optimizer.how_it_works.feature2.badge": {"message": "EXCLUSIVE FEATURE"}, "prompt_optimizer.how_it_works.feature2.title": {"message": "Critical Thinking Assistant"}, "prompt_optimizer.how_it_works.feature2.description": {"message": "Our new Critical Thinking Assistant dropdown menu provides powerful tools to help you think more deeply about AI responses and explore topics from multiple perspectives."}, "prompt_optimizer.how_it_works.feature2.point1": {"message": "Access the \"Critical Thinking Assistant\" dropdown menu in the toolbar of each AI response"}, "prompt_optimizer.how_it_works.feature2.point2": {"message": "Use \"Critical Analysis\" to evaluate AI responses for logical consistency, potential biases, and alternative perspectives"}, "prompt_optimizer.how_it_works.feature2.point3": {"message": "Generate \"Related Questions\" to explore deeper aspects of the topic you might not have considered"}, "prompt_optimizer.how_it_works.feature2.point4": {"message": "Discover \"Related Topics\" to broaden your understanding and explore connected subjects"}, "prompt_optimizer.how_it_works.feature2.image_caption": {"message": "Click to enlarge"}, "prompt_optimizer.how_it_works.critical_analysis.badge": {"message": "CRITICAL ANALYSIS"}, "prompt_optimizer.how_it_works.critical_analysis.title": {"message": "Evaluate AI Responses with Critical Thinking"}, "prompt_optimizer.how_it_works.critical_analysis.description": {"message": "Our Critical Analysis feature helps you think more deeply about AI responses, evaluating them for logical consistency, potential biases, and alternative perspectives."}, "prompt_optimizer.how_it_works.critical_analysis.point1": {"message": "Analyze AI responses for logical consistency and factual accuracy"}, "prompt_optimizer.how_it_works.critical_analysis.point2": {"message": "Identify potential biases and limitations in AI-generated content"}, "prompt_optimizer.how_it_works.critical_analysis.point3": {"message": "Explore alternative perspectives and counterarguments"}, "prompt_optimizer.how_it_works.critical_analysis.point4": {"message": "Develop stronger critical thinking skills for better AI collaboration"}, "prompt_optimizer.how_it_works.critical_analysis.image_caption": {"message": "Click to enlarge"}, "prompt_optimizer.how_it_works.feature4.badge": {"message": "UNLIMITED EXPLORATION"}, "prompt_optimizer.how_it_works.feature4.title": {"message": "Generated Exploration Space"}, "prompt_optimizer.how_it_works.feature4.description": {"message": "Our unique exploration tools help you discover related questions and topics you might not have considered."}, "prompt_optimizer.how_it_works.feature4.point1": {"message": "Explore deeper insights with follow-up questions"}, "prompt_optimizer.how_it_works.feature4.point2": {"message": "Discover broader perspectives through related topics"}, "prompt_optimizer.how_it_works.feature5.badge": {"message": "REFINED QUESTIONS"}, "prompt_optimizer.how_it_works.feature5.title": {"message": "Well-Asked Is Half-Solved"}, "prompt_optimizer.how_it_works.feature5.description": {"message": "Better input means better output. Our tools guide you to write sharper prompts for clearer, more reliable AI results."}, "prompt_optimizer.how_it_works.feature5.point1": {"message": "AI helps you rephrase your question to be clearer and more effective"}, "prompt_optimizer.how_it_works.feature5.point2": {"message": "Refined prompts lead to more accurate and relevant AI responses"}, "prompt_optimizer.how_it_works.feature5.point3": {"message": "Improve outcomes across writing, coding, research, and more"}, "prompt_optimizer.how_it_works.feature5.point4": {"message": "Learn from the optimized prompts to enhance your own questioning skills"}, "prompt_optimizer.how_it_works.feature3.badge": {"message": "SMART FORM"}, "prompt_optimizer.how_it_works.feature3.title": {"message": "Dynamic Form for Missing Information"}, "prompt_optimizer.how_it_works.feature3.description": {"message": "If your instruction is missing key details, the Prompt Optimizer will automatically display a dynamic form. This form guides you to provide the necessary information, ensuring your prompt is clear and complete for the best AI results."}, "prompt_optimizer.how_it_works.feature3.point1": {"message": "Instantly detects missing context or requirements in your prompt"}, "prompt_optimizer.how_it_works.feature3.point2": {"message": "Presents a simple form to fill in the gaps—no guesswork needed"}, "prompt_optimizer.how_it_works.feature3.point3": {"message": "Your optimized prompt is generated with all the required details for better AI answers"}, "prompt_optimizer.how_it_works.cta.title": {"message": "Beyond Prompt Engineering"}, "prompt_optimizer.how_it_works.cta.description": {"message": "Great answers start with great questions. FunBlocks AI Prompt Optimizer goes beyond templated prompts — it refines your queries and uncovers related topics and questions to help you get the most insightful responses."}, "prompt_optimizer.how_it_works.cta.button": {"message": "Download Extension for FREE"}, "prompt_optimizer.why_it_matters.title": {"message": "Why Good Prompts Matter"}, "prompt_optimizer.why_it_matters.description": {"message": "In the age of generative AI, the ability to ask good questions is a crucial skill"}, "prompt_optimizer.why_it_matters.point1.title": {"message": "The Key to Better Answers"}, "prompt_optimizer.why_it_matters.point1.description": {"message": "AI models have vast knowledge and powerful capabilities, but they need clear, specific instructions to deliver their best results. Good prompts unlock their full potential."}, "prompt_optimizer.why_it_matters.point2.title": {"message": "A Critical Human Skill"}, "prompt_optimizer.why_it_matters.point2.description": {"message": "As AI becomes more integrated into our work and lives, the ability to ask good questions and provide clear instructions becomes an increasingly valuable human skill."}, "prompt_optimizer.why_it_matters.point3.title": {"message": "Competitive Advantage"}, "prompt_optimizer.why_it_matters.point3.description": {"message": "Those who can effectively communicate with AI will have a significant advantage in productivity, creativity, and problem-solving in the AI-powered future."}, "prompt_optimizer.why_thinking_matters.title": {"message": "Why Thinking Matters in the Era of Gen AI"}, "prompt_optimizer.why_thinking_matters.description": {"message": "As AI becomes more powerful, human thinking skills become more valuable, not less. The future belongs to those who can think critically and collaborate effectively with AI."}, "prompt_optimizer.why_thinking_matters.point1.title": {"message": "AI Amplifies Human Intelligence"}, "prompt_optimizer.why_thinking_matters.point1.description": {"message": "AI doesn't replace human thinking—it amplifies it. The better your thinking skills, the more effectively you can leverage AI's capabilities to solve complex problems and generate innovative solutions."}, "prompt_optimizer.why_thinking_matters.point2.title": {"message": "Critical Analysis is Essential"}, "prompt_optimizer.why_thinking_matters.point2.description": {"message": "AI can generate vast amounts of information, but humans must evaluate its accuracy, relevance, and implications. Critical thinking skills help you discern quality insights from noise."}, "prompt_optimizer.why_thinking_matters.point3.title": {"message": "Creative Problem-Solving"}, "prompt_optimizer.why_thinking_matters.point3.description": {"message": "While AI excels at pattern recognition and data processing, humans excel at creative thinking, ethical reasoning, and understanding context. These uniquely human skills become more valuable in an AI-driven world."}, "prompt_optimizer.why_thinking_matters.point4.title": {"message": "Human-AI Collaboration"}, "prompt_optimizer.why_thinking_matters.point4.description": {"message": "The future of work is about human-AI collaboration. Those who can think strategically about how to combine human creativity with AI efficiency will lead in their fields."}, "prompt_optimizer.why_thinking_matters.cta.title": {"message": "Develop Your Thinking Skills with AI"}, "prompt_optimizer.why_thinking_matters.cta.description": {"message": "Our Critical Thinking Assistant doesn't just optimize prompts—it helps you develop the thinking skills that will be essential in the AI era."}, "prompt_optimizer.why_thinking_matters.cta.button": {"message": "Start Thinking Better with AI"}, "prompt_optimizer.comparison.title": {"message": "How FunBlocks AI Prompt Optimizer Compares"}, "prompt_optimizer.comparison.description": {"message": "See how our extension stands out from standard AI interactions and other solutions"}, "prompt_optimizer.comparison.note": {"message": "FunBlocks AI Prompt Optimizer combines the best of AI assistance with practical tools that help you develop better prompting skills over time."}, "prompt_optimizer.comparison.funblocksHeader": {"message": "FunBlocks AI Prompt Optimizer"}, "prompt_optimizer.comparison.chatgptHeader": {"message": "Standard ChatGPT"}, "prompt_optimizer.comparison.promptEngineeringHeader": {"message": "Manual Prompt Engineering"}, "prompt_optimizer.comparison.otherExtensionsHeader": {"message": "Other AI Extensions"}, "prompt_optimizer.comparison.feature1": {"message": "One-Click Prompt Optimization"}, "prompt_optimizer.comparison.feature2": {"message": "Dynamic Form for Missing Information"}, "prompt_optimizer.comparison.feature3": {"message": "Related Questions Generation"}, "prompt_optimizer.comparison.feature4": {"message": "Related Topics Exploration"}, "prompt_optimizer.comparison.feature5": {"message": "Multi-Platform Support"}, "prompt_optimizer.comparison.feature6": {"message": "Improves Prompting Skills"}, "prompt_optimizer.comparison.feature7": {"message": "Free Daily Usage"}, "prompt_optimizer.video.title": {"message": "See Prompt Optimizer in Action"}, "prompt_optimizer.video.description": {"message": "Watch how FunBlocks AI Prompt Optimizer transforms your AI interactions"}, "prompt_optimizer.video.cta": {"message": "Try It Yourself"}, "prompt_optimizer.video.feature1.title": {"message": "See It In Action"}, "prompt_optimizer.video.feature1.description": {"message": "Watch how the extension seamlessly integrates with AI chat applications"}, "prompt_optimizer.video.feature2.title": {"message": "Learn Best Practices"}, "prompt_optimizer.video.feature2.description": {"message": "Discover effective prompt optimization techniques you can apply yourself"}, "prompt_optimizer.video.feature3.title": {"message": "Explore Use Cases"}, "prompt_optimizer.video.feature3.description": {"message": "See real-world examples of how optimized prompts improve AI responses"}, "prompt_optimizer.pricing.title": {"message": "Try It For Free"}, "prompt_optimizer.pricing.description": {"message": "Get started with our generous free trial and flexible subscription options"}, "prompt_optimizer.pricing.free_trial.title": {"message": "Free Trial"}, "prompt_optimizer.pricing.free_trial.feature1": {"message": "30 free optimizations for new users"}, "prompt_optimizer.pricing.free_trial.feature2": {"message": "10 free optimizations daily"}, "prompt_optimizer.pricing.free_trial.feature3": {"message": "Access to all core features"}, "prompt_optimizer.pricing.free_trial.note": {"message": "No credit card required"}, "prompt_optimizer.pricing.subscription.title": {"message": "FunBlocks AI Plans"}, "prompt_optimizer.pricing.subscription.feature1": {"message": "Included in all FunBlocks AI subscription plans"}, "prompt_optimizer.pricing.subscription.feature2": {"message": "Choose the plan that fits your needs"}, "prompt_optimizer.pricing.subscription.feature3": {"message": "Access to the entire FunBlocks AI ecosystem"}, "prompt_optimizer.pricing.subscription.note": {"message": "See pricing page for details"}, "prompt_optimizer.testimonials.title": {"message": "What Our Users Say"}, "prompt_optimizer.testimonials.description": {"message": "Discover how FunBlocks AI Prompt Optimizer is transforming the way people interact with AI"}, "prompt_optimizer.testimonials.user1.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user1.role": {"message": "Content Creator"}, "prompt_optimizer.testimonials.user1.text": {"message": "\"This extension has completely changed how I work with AI. I used to struggle getting ChatGPT to understand exactly what I needed, but now the prompt optimization feature helps me clarify my requests. The related questions feature has also helped me explore topics I wouldn't have thought of on my own.\""}, "prompt_optimizer.testimonials.user2.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user2.role": {"message": "Researcher"}, "prompt_optimizer.testimonials.user2.text": {"message": "\"As someone who uses AI tools daily for research, the ability to optimize my prompts has been invaluable. I'm getting more precise information and spending less time refining my questions. The related topics feature has also led me to discover connections I might have missed otherwise.\""}, "prompt_optimizer.testimonials.user3.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user3.role": {"message": "Student"}, "prompt_optimizer.testimonials.user3.text": {"message": "\"I've noticed my own questioning skills improving since I started using this extension. By seeing how it transforms my basic questions into more specific ones, I'm learning to think more critically about what I'm really trying to ask. It's like having a personal tutor for asking better questions!\""}, "prompt_optimizer.testimonials.user4.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user4.role": {"message": "Software Developer"}, "prompt_optimizer.testimonials.user4.text": {"message": "\"As a developer, I rely on AI tools for documentation, debugging, and learning new frameworks. The Prompt Optimizer has dramatically improved my workflow by helping me formulate precise technical questions. The dynamic form feature is particularly useful when I need to provide code context - it ensures I include all the necessary details for accurate responses.\""}, "prompt_optimizer.testimonials.user5.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user5.role": {"message": "Digital Marketer"}, "prompt_optimizer.testimonials.user5.text": {"message": "\"This extension has become essential for my content creation process. When brainstorming ideas or researching topics, the related questions feature helps me explore angles I wouldn't have considered. I'm creating more comprehensive, well-researched content in less time, and my clients are noticing the difference in quality.\""}, "prompt_optimizer.testimonials.user6.name": {"message": "Dr. <PERSON>"}, "prompt_optimizer.testimonials.user6.role": {"message": "Healthcare Professional"}, "prompt_optimizer.testimonials.user6.text": {"message": "\"In healthcare, precision is crucial. This extension helps me formulate clear, specific queries when researching medical topics or treatment options. The ability to optimize prompts across different AI platforms is invaluable, as I can choose the model best suited for particular types of medical information while maintaining consistent, high-quality interactions.\""}, "prompt_optimizer.cta.title": {"message": "Ready to enhance your AI conversations?"}, "prompt_optimizer.cta.description": {"message": "Download our Chrome extension now and start getting better results from AI"}, "prompt_optimizer.cta.button": {"message": "Download Extension for FREE"}, "prompt_optimizer.browser_not_supported": {"message": "This extension is only supported on Chrome and Edge browsers. Please use Chrome or the new version of Edge to install the extension."}, "prompt_optimizer.faq.q1": {"message": "Which AI platforms does the Prompt Optimizer Extension work with?"}, "prompt_optimizer.faq.a1": {"message": "The extension currently works with ChatGPT, Claude, Gemini, Perplexity, DeepSeek, and other popular AI chat applications. We're continuously adding support for more platforms."}, "prompt_optimizer.faq.q2": {"message": "How does the free trial work?"}, "prompt_optimizer.faq.a2": {"message": "New users receive 30 free optimizations when they first install the extension. Additionally, all users receive 10 free optimizations daily, regardless of subscription status."}, "prompt_optimizer.faq.q3": {"message": "Do I need to create a FunBlocks account to use the extension?"}, "prompt_optimizer.faq.a3": {"message": "Yes, registering a FunBlocks account (free) allows you to access new user rewards and use it across devices."}, "prompt_optimizer.faq.q4": {"message": "How does the extension improve my prompt skills?"}, "prompt_optimizer.faq.a4": {"message": "By showing you optimized versions of your prompts, you'll learn effective prompt patterns and techniques. Over time, you'll naturally incorporate these improvements into your own prompt style, becoming more effective at communicating with AI."}, "prompt_optimizer.faq.q5": {"message": "Is my data secure when using the extension?"}, "prompt_optimizer.faq.a5": {"message": "Yes. We take privacy very seriously. The extension only processes the specific prompts you choose to optimize. We do not store your conversation history or share your data with third parties. All processing is done securely through our API."}, "prompt_optimizer.faq.q6": {"message": "How does this differ from directly using ChatGPT to improve my prompts?"}, "prompt_optimizer.faq.a6": {"message": "Although you can ask ChatGPT to help improve your prompts, our extension offers several advantages: 1) It integrates directly into the interface, saving you time and extra steps, 2) It's specifically designed for prompt optimization, with specialized algorithms, 3) It provides additional features like related questions and topic exploration, 4) It's applicable to multiple AI platforms, not just ChatGPT."}, "prompt_optimizer.faq.q7": {"message": "How does the extension enhance user thinking abilities?"}, "prompt_optimizer.faq.a7": {"message": "The extension's primary function aims to enhance users' thinking and problem-solving abilities. Through AI-optimized prompts, users can obtain better questions, improving their questioning ability; simultaneously, AI-generated related topics and questions also expand users' multi-angle thinking capabilities on specific topics or questions. Finally, AI-optimized prompts also help users better describe their needs."}, "prompt_optimizer.faq.q8": {"message": "How does the dynamic form feature for missing information work?"}, "prompt_optimizer.faq.a8": {"message": "When you select 'Optimize Prompt', our AI analyzes your prompt, identifying crucial information missing for high-quality answers. If information gaps are detected, the extension generates a customized form containing specific fields for the missing details. Completing the form integrates these details seamlessly into your optimized prompt, ensuring AI has everything needed to provide accurate and comprehensive answers."}, "prompt_optimizer.faq.q9": {"message": "Can the extension work offline or does it require an internet connection?"}, "prompt_optimizer.faq.a9": {"message": "The Prompt Optimizer extension requires an internet connection to function, as it communicates with our optimization servers. This ensures you always have access to the latest optimization algorithms and features. As long as you're logged into your FunBlocks account, your optimization counts will be tracked across devices, regardless of which device you use."}, "prompt_optimizer.faq.q10": {"message": "What is lazy prompting?"}, "prompt_optimizer.faq.a10": {"message": "Lazy prompting is a method that first provides a large language model (LLM) with a quick, imprecise prompt, then evaluates the output quality, and finally provides more context as needed. Its goal is to explore how much context can be omitted while still obtaining useful results. Meanwhile, the LLM may exhibit questions or solutions you hadn't thought of."}, "prompt_optimizer.faq.q10e": {"message": "Does the FunBlocks AI Prompt Optimizer support lazy prompting?"}, "prompt_optimizer.faq.a10e": {"message": "The FunBlocks AI Prompt Optimizer is an ideal tool for practicing lazy prompting. Users only need to propose a simple idea or question, then click the FunBlocks AI Prompt Optimizer button, and the system will one-click complete the expansion and exploration of the prompt, helping users discover more possibilities and unthought-of directions and solutions."}, "prompt_optimizer.faq.q11": {"message": "How does the Prompt Optimizer extension handle professional technical or domain-specific terminology?"}, "prompt_optimizer.faq.a11": {"message": "Our extension is designed to preserve and correctly contextualize various domain-specific professional terminology, including programming, medicine, law, finance, and scientific research. When optimizing prompts containing technical terms, the system maintains precise terminology while enhancing the surrounding context structure and clarity. For highly specialized fields, the dynamic form feature may request additional context to ensure accurate optimization."}, "prompt_optimizer.faq.q12": {"message": "What is the core value of the Prompt Optimizer extension?"}, "prompt_optimizer.faq.a12": {"message": "We developed this product to help you communicate better with AI, improving the quality and answers generated. At the same time, using the FunBlocks AI Prompt Optimizer also enhances users' higher-order thinking abilities, such as questioning ability and multi-angle thinking."}, "homepage.testimonials.user3.name": {"message": "<PERSON>"}, "homepage.testimonials.user3.role": {"message": "Teacher"}, "homepage.testimonials.user3.text": {"message": "\"My productivity as a teacher has reached new heights with FunBlocks AI Flow! This incredible tool has become my go-to assistant, helping me dive deeper into content and deliver clearer, more engaging lessons for my students. I'm so impressed with its capabilities that I've decided to share it with my fellow educators during a webinar I'm hosting next week. FunBlocks AI Flow is an absolute powerhouse—it's a game-changer for any teacher looking to enhance their teaching and streamline their workflow!\""}, "homepage.testimonials.user4.name": {"message": "<PERSON>."}, "homepage.testimonials.user4.role": {"message": "Grad Student"}, "homepage.testimonials.user4.text": {"message": "\"As a research student, organizing complex information is crucial. FunBlocks AI helps me structure my research visually and generate comprehensive literature reviews. The AI assistance has opened my eyes to connections between concepts I might have missed. It's like having a research partner who never sleeps!\""}, "homepage.testimonials.user5.name": {"message": "<PERSON>"}, "homepage.testimonials.user5.role": {"message": "Marketing Manager"}, "homepage.testimonials.user5.text": {"message": "\"Using FunBlocks AIFlow for brainstorming has helped me break through mental barriers and generate breakthrough ideas, leading to innovative solutions for our marketing strategies. The mind map generator, especially, is an absolute must-have for any creative professional!\""}, "homepage.testimonials.user6.name": {"message": "<PERSON>"}, "homepage.testimonials.user6.role": {"message": "Student"}, "homepage.testimonials.user6.text": {"message": "\"As a student, FunBlocks AI has significantly enhanced my learning capabilities. It aids in thematic learning, allowing me to explore and delve deeper into subjects, ultimately improving my critical thinking skills.\""}, "homepage.cta.title": {"message": "Ready to Embark on a Knowledge Adventure?"}, "homepage.cta.subtitle": {"message": "Join <PERSON><PERSON><PERSON>s <PERSON> and unleash your limitless cognitive potential!"}, "homepage.cta.button": {"message": "Start Your Free Trial Now"}, "homepage.faq.title": {"message": "Frequently Asked Questions"}, "homepage.faq.q0": {"message": "What is FunBlocks?"}, "homepage.faq.a0": {"message": "FunBlocks is an AI-driven knowledge learning and productivity platform that offers an all-in-one AI workspace. Users can leverage the AI assistant to write documents and notes, conduct mind mapping and whiteboard brainstorming, engage in topic-based learning, and create slides in Markdown format with a single click to generate infographics. Additionally, FunBlocks provides a browser extension to assist users with AI-powered writing and reading on any webpage."}, "homepage.faq.q01": {"message": "Why does the homepage emphasize FunBlocks AIFlow?"}, "homepage.faq.a01": {"message": "The homepage highlights FunBlocks AIFlow because it showcases the platform's powerful features for improving learning, creativity, and productivity right from the start. FunBlocks AIFlow uses a visual AI whiteboard and mind mapping to inspire creativity and allows users to brainstorm with AI. With just one click, users can turn their mind maps into practical outputs like slides and proposals, which are automatically saved in the FunBlocks workspace for easy editing. This setup encourages users to explore other FunBlocks products, such as FunBlocks AI Docs and FunBlocks AI Slides."}, "homepage.faq.q1": {"message": "What is FunBlocks AIFlow?"}, "homepage.faq.a1": {"message": "FunBlocks AIFlow is an AI-driven visual thinking platform designed to help users brainstorm and generate innovative ideas. It can transform complex concepts into mind maps, slides, and infographics, combining the powerful capabilities of AI with visual thinking techniques to significantly enhance your cognitive abilities and productivity."}, "homepage.faq.q2": {"message": "How does AIFlow differ from ChatGPT?"}, "homepage.faq.a2": {"message": "ChatGPT provides linear text conversations, while AIFlow offers a visual and multidimensional workspace. You can explore ideas in multiple directions simultaneously, maintain context through visual hierarchies, and instantly convert your thoughts into professional deliverables."}, "homepage.faq.q3": {"message": "What can I create with AIFlow?"}, "homepage.faq.a3": {"message": "With AIFlow, you can create mind maps, presentation slides, infographics, strategic plans, knowledge maps, and visual analyses of complex topics. The platform supports various thinking frameworks and allows you to convert your visual creations into different formats."}, "homepage.faq.q4": {"message": "What other products does FunBlocks offer besides AIFlow?"}, "homepage.faq.a4": {"message": "FunBlocks provides a comprehensive suite of AI tools and a workspace designed to enhance productivity and spark creativity. It integrates AI-powered mind mapping, document writing, and slide generation features to create a holistic knowledge work environment.\n\nKey products include:\nFunBlocks AI Flow: An AI-driven mind mapping tool for creativity and knowledge exploration.\nFunBlocks AI Docs: An AI-powered Notion-style block editor that offers an AI writing assistant for document creation.\nFunBlocks AI Slides: An AI-driven slide generation tool that creates slides with one click, providing content writing and presentation guidance.\nFunBlocks Browser Extension: An efficiency-boosting browser extension that supports AI writing, reading, email replies, and more.\nFunBlocks AI Tools: Professionally designed AI tools for instant access, providing solutions for specific use cases, including AI graphics, AI mind maps, AI brainstorming, AI slides, AI decision analyzers, AI task planners, and AI YouTube summarizers.\nAI Assistant: A customizable AI assistant that supports custom prompts and integrates with AI Flow, AI Slides, AI Docs, and the browser extension."}, "homepage.faq.q5": {"message": "Is FunBlocks suitable for team collaboration?"}, "homepage.faq.a5": {"message": "Yes, FunBlocks is suitable for both individual and team use. Teams can collaborate in shared workspaces for brainstorming, document writing, and more, while maintaining a shared knowledge base with the assistance of AI."}, "homepage.faq.q6": {"message": "Do I need technical skills to use FunBlocks AI?"}, "homepage.faq.a6": {"message": "No technical skills are required. FunBlocks AI features an intuitive interface that allows you to easily start creating boards or documents and use the AI assistant for learning and creation."}, "homepage.faq.q7": {"message": "Which industries or roles can benefit from FunBlocks AI?"}, "homepage.faq.a7": {"message": "FunBlocks AI is valuable for educators, students, business professionals, researchers, writers, and anyone engaged in knowledge work. It is particularly suitable for strategic planning, content creation, learning, problem-solving, and decision-making."}, "homepage.faq.q8": {"message": "Is AIFlow just another AI mind mapping tool? How does it differ from regular AI mind mapping tools?"}, "homepage.faq.a8": {"message": "While AIFlow does include AI-assisted mind mapping features, it offers much more than that. Compared to regular AI mind mapping tools (like Xmind, FreeMind, MindMeister, etc.), AIFlow provides a more comprehensive and powerful feature set:\n1. Boundless whiteboard: Not limited to tree structures, allowing free layout and connection of ideas.\n2. Diverse node types: Supports images, notes, task lists, etc., each with dedicated AI assistants.\n3. One-click content generation: Quickly transform mind maps into articles, plans, and other practical content.\n4. Proactive AI inspiration: AI can actively guide you in exploring relevant topics and critical thinking related to your themes and questions, sparking innovative ideas.\n5. Customizable AI prompts: Allows you to create AI assistants tailored to specific tasks without programming knowledge.\nThese features make AIFlow a more comprehensive tool for creativity and productivity, suitable for various scenarios such as learning, work, and creation."}, "homepage.faq.q9": {"message": "What advantages does AIFlow offer in assisting thinking?"}, "homepage.faq.a9": {"message": "AIFlow has multiple advantages in aiding thought processes:\n1. Multidimensional analysis: Helps you examine problems from multiple angles through mind maps and AI suggestions.\n2. Breaking thought limitations: AI can provide unexpected insights, helping you break free from conventional thinking patterns.\n3. Knowledge connections: AI can help you establish links between different fields, fostering innovative thinking.\n4. Application of classic thinking models: AIFlow supports various classic thinking models, helping you think within clear and efficient frameworks.\n5. Problem decomposition: Assists you in breaking down complex problems into manageable parts for gradual resolution.\n6. Knowledge exploration: Input a topic, and AI can guide you in exploring related fields, expanding your knowledge base.\nThese features make AIFlow a powerful tool for enhancing your analytical and innovative capabilities."}, "homepage.faq.q10": {"message": "What advantages does using FunBlocks AIFlow for brainstorming offer?"}, "homepage.faq.a10": {"message": "FunBlocks AIFlow simplifies the brainstorming process using AI technology, enabling rapid generation of comprehensive mind maps that help you easily gather a wealth of ideas and creativity. Its flexible interactive interface allows you to explore more ideas from different angles with the AI assistant. Additionally, AIFlow incorporates classic thinking models (such as Six Thinking Hats, McKinsey Method, SCAMPER, SWOT Analysis, perspective shifting, reverse thinking, Business Model Canvas, and Value Proposition Canvas), fully leveraging the capabilities of large language models to help you generate novel ideas."}, "homepage.faq.q11": {"message": "Does a paid membership for FunBlocks AI apply to all FunBlocks AI products and services?"}, "homepage.faq.a11": {"message": "Yes, unless otherwise specified, the permissions and AI access quotas granted by a paid membership for FunBlocks AI apply to all products and services on the platform."}, "homepage.faq.q12": {"message": "If I use my own API Key (such as ChatGPT, Claude, Gemini, Groq, etc.), do I still need to pay for FunBlocks AI services?"}, "homepage.faq.a12": {"message": "No, you do not. If you use your own API Key, you only need to pay the service provider. FunBlocks AI does not charge additional fees and is not subject to daily free access quotas."}, "homepage.faq.q14": {"message": "What types of creative work is FunBlocks AIFlow suitable for?"}, "homepage.faq.a14": {"message": "FunBlocks AIFlow is particularly suitable for tasks that require multi-angle thinking and creative exploration, such as brainstorming, topic research, and content planning. It combines the powerful understanding and reasoning capabilities of AI to facilitate multi-threaded interactions in an infinite canvas space, helping users delve deeper into exploration and expand their thinking."}, "homepage.faq.q15": {"message": "Why does FunBlocks AI support all major large language models?"}, "homepage.faq.a15": {"message": "FunBlocks AI is committed to providing users with flexible choices and the best experience. By supporting all major large language models, we meet your diverse needs, ensuring you can choose the most suitable AI model based on personal preferences and workflows, thereby enhancing work efficiency and creativity."}, "homepage.faq.q16": {"message": "Can I try FunBlocks AI for free?"}, "homepage.faq.a16": {"message": "Yes, FunBlocks AI offers a free trial. New users will receive 30 free AI service accesses upon registration, along with an additional 10 free accesses daily, allowing you to fully experience the transformative assistance of FunBlocks AI in learning and work. Additionally, FunBlocks AI has a reward mechanism where you and your friends can receive extra free usage credits by inviting friends to register."}, "homepage.faq.q17": {"message": "How can I earn free usage credits by inviting friends?"}, "homepage.faq.a17": {"message": "You can invite friends in two ways. First, share the invitation link, and your friend will receive a reward upon registering through the link. Second, share the FunBlocks AI browser extension or content generated by AI Flow; if users click to read and register, you will also receive an invitation reward."}, "homepage.faq.q18": {"message": "How can I cancel my subscription plan?"}, "homepage.faq.a18": {"message": "You can find the \"Upgrade AI Membership\" and \"Upgrade FunBlocks\" sections in the settings. If you have subscribed to a membership plan, there will be a Cancel button in the corresponding description area that you can click to unsubscribe."}, "homepage.faq.q19": {"message": "Is my data safe on the FunBlocks platform?"}, "homepage.faq.a19": {"message": "We prioritize data security and privacy. All data is encrypted, and we follow industry best practices to protect it. You have complete control over your content and can manage sharing permissions."}, "aiflow.head.title": {"message": "Visualized Chat with AI. Best for brainstorming, problem solving, critical & creative thinking"}, "aiflow.head.description": {"message": "Go beyond simple text chats – visualize ideas and AI insights on a boundless canvas using advanced models like GPT & Claude. Effortlessly break down complex problems, brainstorm with structured methods (SWOT, Six Thinking Hats), generate instant mind maps from books, videos, or webpages, and create ready-to-use outputs like presentations and documents. Ideal for learning, strategic planning, content creation, and in-depth analysis. Enhance your thinking, explore topics deeply, and achieve more with your intelligent AI partner. Supercharge creativity and productivity now with FunBLocks AIFlow!"}, "aiflow.nav.home": {"message": "FunBlocks AIFlow"}, "aiflow.nav.intro": {"message": "AIFlow"}, "aiflow.nav.features": {"message": "Key Features"}, "aiflow.nav.ai-powered-brainstorming": {"message": "Brainstorming"}, "aiflow.nav.explore-with-ai": {"message": "Exploration"}, "aiflow.nav.use-cases": {"message": "Use Cases"}, "aiflow.nav.faq": {"message": "FAQ"}, "aiflow.nav.pricing": {"message": "Pricing"}, "aiflow.nav.cta": {"message": "<PERSON><PERSON>"}, "aiflow.masthead.title_2": {"message": "A New Way to Interact with AI"}, "aiflow.masthead.subtitle": {"message": "Go Beyond Chat. Transform Chatbox and Text Thread into a Boundless Canvas. Visualize Ideas, Solve Problems, and <PERSON><PERSON> Faster with Integrated Brainstorming, Mind Mapping, Critical Thinking, and Creative Thinking AI Tools"}, "aiflow.masthead.cta": {"message": "Start Free Trial"}, "aiflow.masthead.learn_more": {"message": "Explore Features"}, "aiflow.thinking_methods.brainstorming": {"message": "Brainstorming"}, "aiflow.thinking_methods.mind_mapping": {"message": "Mind Mapping"}, "aiflow.thinking_methods.critical_thinking": {"message": "Critical Thinking"}, "aiflow.thinking_methods.creative_thinking": {"message": "Creative Thinking"}, "aiflow.thinking-methods.title": {"message": "Enhance Your Thinking with AIFlow"}, "aiflow.thinking-methods.description": {"message": "FunBlocks AIFlow integrates powerful thinking methodologies with AI to enhance your cognitive abilities"}, "aiflow.thinking-methods.aiflow-features": {"message": "AIFlow Features:"}, "aiflow.thinking-methods.brainstorming.title": {"message": "Brainstorming"}, "aiflow.thinking-methods.brainstorming.description": {"message": "Generate a wide range of ideas without judgment"}, "aiflow.thinking-methods.brainstorming.feature1": {"message": "AI-assisted idea generation"}, "aiflow.thinking-methods.brainstorming.feature2": {"message": "Classic thinking models integration"}, "aiflow.thinking-methods.brainstorming.feature3": {"message": "Visual organization of ideas"}, "aiflow.thinking-methods.brainstorming.feature4": {"message": "One-click expansion of concepts"}, "aiflow.thinking-methods.mind_mapping.title": {"message": "Mind Mapping"}, "aiflow.thinking-methods.mind_mapping.description": {"message": "Organize information visually to see connections"}, "aiflow.thinking-methods.mind_mapping.feature1": {"message": "Infinite canvas for complex maps"}, "aiflow.thinking-methods.mind_mapping.feature2": {"message": "Hierarchical node structure"}, "aiflow.thinking-methods.mind_mapping.feature3": {"message": "AI-generated connections"}, "aiflow.thinking-methods.mind_mapping.feature4": {"message": "Visual customization options"}, "aiflow.thinking-methods.critical_thinking.title": {"message": "Critical Thinking"}, "aiflow.thinking-methods.critical_thinking.description": {"message": "Analyze information objectively to form reasoned judgments"}, "aiflow.thinking-methods.critical_thinking.feature1": {"message": "Cognitive bias identification"}, "aiflow.thinking-methods.critical_thinking.feature2": {"message": "Assumption testing tools"}, "aiflow.thinking-methods.critical_thinking.feature3": {"message": "Multiple perspective analysis"}, "aiflow.thinking-methods.critical_thinking.feature4": {"message": "Structured evaluation frameworks"}, "aiflow.thinking-methods.creative_thinking.title": {"message": "Creative Thinking"}, "aiflow.thinking-methods.creative_thinking.description": {"message": "Develop innovative solutions and unique perspectives"}, "aiflow.thinking-methods.creative_thinking.feature1": {"message": "Lateral thinking prompts"}, "aiflow.thinking-methods.creative_thinking.feature2": {"message": "Analogical reasoning tools"}, "aiflow.thinking-methods.creative_thinking.feature3": {"message": "Constraint removal exercises"}, "aiflow.thinking-methods.creative_thinking.feature4": {"message": "Idea combination techniques"}, "aiflow.case-studies.title": {"message": "AIFlow in Action: Real-World Applications"}, "aiflow.case-studies.description": {"message": "See how professionals and students use FunBlocks AIFlow to enhance their thinking and productivity"}, "aiflow.case-studies.education.title": {"message": "Educational Excellence"}, "aiflow.case-studies.education.description": {"message": "A university professor used AIFlow to help students visualize complex concepts in cognitive psychology. Students created mind maps to connect theories, research findings, and real-world applications, resulting in 40% better comprehension compared to traditional note-taking."}, "aiflow.case-studies.business.title": {"message": "Business Innovation"}, "aiflow.case-studies.business.description": {"message": "A product development team at a tech startup used AIFlow's brainstorming and critical thinking tools to identify market gaps and develop innovative solutions. The visual approach helped them reduce planning time by 60% while generating 3x more viable product ideas."}, "aiflow.educational-resources.title": {"message": "Educational Resources"}, "aiflow.educational-resources.description": {"message": "Enhance your thinking skills with our comprehensive guides and tutorials"}, "aiflow.educational-resources.learn_more": {"message": "Learn More"}, "aiflow.educational-resources.resource1.title": {"message": "Brainstorming Techniques"}, "aiflow.educational-resources.resource1.description": {"message": "Learn effective brainstorming methods enhanced by AI"}, "aiflow.educational-resources.resource2.title": {"message": "Mind Mapping Mastery"}, "aiflow.educational-resources.resource2.description": {"message": "Discover how to create powerful mind maps for any purpose"}, "aiflow.educational-resources.resource3.title": {"message": "Critical Thinking Skills"}, "aiflow.educational-resources.resource3.description": {"message": "Enhance your analytical abilities with structured approaches"}, "aiflow.educational-resources.resource4.title": {"message": "Creative Problem Solving"}, "aiflow.educational-resources.resource4.description": {"message": "Break through creative blocks with innovative techniques"}, "aiflow.educational-resources.resource5.title": {"message": "Mental Models Library"}, "aiflow.educational-resources.resource5.description": {"message": "Access powerful thinking frameworks for complex problems"}, "aiflow.educational-resources.resource6.title": {"message": "Integrated Workflow Guide"}, "aiflow.educational-resources.resource6.description": {"message": "See how all thinking methods work together in AIFlow"}, "aiflow.comparison.title": {"message": "How <PERSON><PERSON><PERSON> Compares to Other Tools"}, "aiflow.comparison.description": {"message": "See how FunBlocks AIFlow stands out in the thinking tools landscape"}, "aiflow.comparison.feature1": {"message": "Brainstorming Capabilities"}, "aiflow.comparison.feature2": {"message": "Mind Mapping"}, "aiflow.comparison.feature3": {"message": "Critical Thinking Tools"}, "aiflow.comparison.feature4": {"message": "Creative Thinking Frameworks"}, "aiflow.comparison.feature5": {"message": "Visual Thinking Integration"}, "aiflow.comparison.advanced": {"message": "✅ Advanced"}, "aiflow.comparison.basic": {"message": "✅ Basic"}, "aiflow.comparison.no": {"message": "❌"}, "aiflow.comparison.note": {"message": "FunBlocks AIFlow integrates various thinking methodologies and AI support, particularly excelling in advanced brainstorming, mind mapping, critical thinking, and visual integration compared to traditional AI chatbots and general-purpose mindmapping or document tools."}, "aiflow.intro.title": {"message": "Beyond ChatGPT"}, "aiflow.intro.description": {"message": "Discover innovative ways to engage with AI beyond text. Visualize your thoughts and AI outputs in formats that enhance your cognitive process."}, "aiflow.intro.point1.name": {"message": "<PERSON>und<PERSON>"}, "aiflow.intro.point1.description": {"message": "Break free from traditional conversation limits, brainstorm in infinite space"}, "aiflow.intro.point2.name": {"message": "Multidimensional Thinking"}, "aiflow.intro.point2.description": {"message": "Analyze problems comprehensively with multidimensional mind maps"}, "aiflow.intro.point3.name": {"message": "State-of-the-Art AI Assistants"}, "aiflow.intro.point3.description": {"message": "Integrated with advanced AI like GPT-4o and Claude-3.7 for intelligent support"}, "aiflow.intro.point4.name": {"message": "Versatile Applications"}, "aiflow.intro.point4.description": {"message": "AI Agents ready for learning, work, and creative projects"}, "aiflow.features.title": {"message": "Key Features"}, "aiflow.features.item1.name": {"message": "Break Down Complex Problems"}, "aiflow.features.item1.description": {"message": "Quickly decompose information, start researching with ease"}, "aiflow.features.item1.li1": {"message": "Break complex problems into manageable parts"}, "aiflow.features.item1.li2": {"message": "Rapidly identify key elements and potential challenges"}, "aiflow.features.item1.li3": {"message": "Simplify the research process through a divide-and-conquer approach"}, "aiflow.features.item2.name": {"message": "Expand Your Thinking"}, "aiflow.features.item2.description": {"message": "Discover new perspectives, break through mental barriers"}, "aiflow.features.item2.li1": {"message": "Generate multiple perspectives on any topic"}, "aiflow.features.item2.li2": {"message": "Let AI suggest unexpected angles and approaches, spark innovative ideas and solutions"}, "aiflow.features.item2.li3": {"message": "Discover connections and insights you might have missed, broadening your thinking"}, "aiflow.features.item2.li4": {"message": "Delve deep into related concepts and fields"}, "aiflow.features.item3.name": {"message": "Clear Instructions for Better Results"}, "aiflow.features.item3.description": {"message": "AIFlow enhances communication between you and AI with various innovative features."}, "aiflow.features.item3.li1": {"message": "Not good at asking questions? Let AI help you analyze and refine your queries."}, "aiflow.features.item3.li2": {"message": "Unclear about your requirements? AI will analyze your instructions and generate a confirmation form for you to review."}, "aiflow.features.item3.li3": {"message": "Struggling to describe artistic terms for image generation? AIFlow generates professional art descriptions based on your simple input to help you create image prompts."}, "aiflow.features.item4.name": {"message": "Diverse Content Support"}, "aiflow.features.item4.description": {"message": "Support for images, notes, task lists, links, and more, each with a dedicated AI assistant"}, "aiflow.features.item4.li1": {"message": "Image: AI analysis of visuals, from art to whiteboards"}, "aiflow.features.item4.li2": {"message": "Note: Quick idea capture with AI-powered expansion"}, "aiflow.features.item4.li3": {"message": "Task list: AI-enhanced task management and prioritization"}, "aiflow.features.item4.li4": {"message": "Link: Web content integration for research and inspiration"}, "aiflow.features.item5.name": {"message": "Group Nodes"}, "aiflow.features.item5.description": {"message": "Not just a new node, but the gateway to more application scenarios."}, "aiflow.features.item5.li1": {"message": "Organize related concepts into cohesive clusters for enhanced comprehension"}, "aiflow.features.item5.li2": {"message": "Leverage AI assistant support for analysis and generation"}, "aiflow.features.item5.li3": {"message": "Synthesize diverse ideas into comprehensive solutions with one-click"}, "aiflow.features.item6.name": {"message": "All-in-One AI Assistant"}, "aiflow.features.item6.description": {"message": "Multiple AI features ready to use, plus customizable AI prompts."}, "aiflow.features.item6.li1": {"message": "In-depth analysis of needs, providing insightful suggestions that significantly enhance the depth and breadth of thinking"}, "aiflow.features.item6.li2": {"message": "Automatically generate high-quality content, saving time and effort"}, "aiflow.features.item6.li3": {"message": "Support personalized AI instructions to meet specific needs"}, "aiflow.ai-powered-brainstorming.title": {"message": "AI-Enhanced Brainstorming"}, "aiflow.ai-powered-brainstorming.subtitle": {"message": "Unleash creativity through AI-driven classic mental models and organized frameworks"}, "aiflow.ai-powered-brainstorming.classic_models.title": {"message": "Ideation Using Classic Mental Models"}, "aiflow.ai-powered-brainstorming.classic_models.subtitle": {"message": "Harness AI for brainstorming with structured methodologies"}, "aiflow.ai-powered-brainstorming.classic_models.m1": {"message": "Six Thinking Hats"}, "aiflow.ai-powered-brainstorming.classic_models.m2": {"message": "SWOT Analysis"}, "aiflow.ai-powered-brainstorming.classic_models.m3": {"message": "<PERSON><PERSON><PERSON><PERSON>ey Method"}, "aiflow.ai-powered-brainstorming.classic_models.m4": {"message": "First Principles ..."}, "aiflow.ai-powered-brainstorming.oneclick_generation.title": {"message": "Instant Work Outputs"}, "aiflow.ai-powered-brainstorming.oneclick_generation.subtitle": {"message": "Generate usable work results directly from brainstorming mind maps"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m1": {"message": "Presentation Slides"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m2": {"message": "Solution Documents"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m3": {"message": "Infographics"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m4": {"message": "Images ..."}, "aiflow.book-insights.title": {"message": "Instant Mind Maps from Books, Movies, and More with AI"}, "aiflow.book-insights.subtitle": {"message": "Effortlessly Generate Mind Maps to Enhance Understanding and Insights"}, "aiflow.book-insights.features.title": {"message": "Unlock Insights Efficiently from Various Media"}, "aiflow.book-insights.features.li1": {"message": "Generate mind maps instantly from book titles, movie names, artworks, images, webpages and YouTube videos."}, "aiflow.book-insights.features.li2": {"message": "Enhance your appreciation and interpretation of works with AI-driven visualizations."}, "aiflow.book-insights.features.li3": {"message": "Uncover valuable insights and content to enrich your learning experience."}, "aiflow.book-insights.features.li4": {"message": "Facilitate efficient reading and learning, allowing for deeper engagement with materials."}, "aiflow.book-insights.features.li5": {"message": "Broaden your thinking depth and breadth through structured analysis and exploration."}, "aiflow.explore-with-ai.title": {"message": "Explore the World with AI"}, "aiflow.explore-with-ai.description": {"message": "Open up broader cognitive horizons for every topic and explore endless possibilities"}, "aiflow.explore-with-ai.point1.name": {"message": "Expand Your Exploration Space"}, "aiflow.explore-with-ai.point1.description": {"message": "Create a comprehensive exploration space that supports in-depth research or broad expansion on any topic"}, "aiflow.explore-with-ai.point2.name": {"message": "<PERSON><PERSON><PERSON>"}, "aiflow.explore-with-ai.point2.description": {"message": "Provide not only accurate answers for specific topics but also showcase related knowledge and a holistic perspective"}, "aiflow.explore-with-ai.point3.name": {"message": "Recommend Related Topics"}, "aiflow.explore-with-ai.point3.description": {"message": "In addition to answering current questions, AIFlow intelligently generates follow-up questions or topics to guide you into uncharted territories"}, "aiflow.explore-with-ai.point4.name": {"message": "Collaborative Exploration Journey"}, "aiflow.explore-with-ai.point4.description": {"message": "Collaborate with you in exploration, spark inspiration, and continuously advance your thinking"}, "aiflow.use-cases.title": {"message": "Practical Applications"}, "aiflow.use-cases.description": {"message": "AIFlow is designed for various knowledge work scenarios, streamlining your thought process from initial exploration to final execution."}, "aiflow.use-cases.case1.title": {"message": "Learning by Topics"}, "aiflow.use-cases.case1.description": {"message": "Create detailed knowledge frameworks and uncover connections across different fields."}, "aiflow.use-cases.case2.title": {"message": "Strategic Work Planning"}, "aiflow.use-cases.case2.description": {"message": "Generate creative ideas, break down tasks, and develop solutions for complex challenges."}, "aiflow.use-cases.case3.title": {"message": "In-Depth Content Analysis"}, "aiflow.use-cases.case3.description": {"message": "Analyze films, books, and artworks thoroughly using mind mapping techniques."}, "aiflow.use-cases.case4.title": {"message": "Thorough Problem Analysis"}, "aiflow.use-cases.case4.description": {"message": "Visualize complex issues from different perspectives to find innovative solutions."}, "aiflow.use-cases.case5.title": {"message": "Content Creation"}, "aiflow.use-cases.case5.description": {"message": "Convert brainstorming mind maps into well-structured documents and presentations."}, "aiflow.use-cases.case6.title": {"message": "Enhancing Thinking Skills"}, "aiflow.use-cases.case6.description": {"message": "Continuously develop your critical and creative thinking skills."}, "aiflow.testimonials.user1.name": {"message": "<PERSON>"}, "aiflow.testimonials.user1.role": {"message": "Marketing Manager"}, "aiflow.testimonials.user1.text": {"message": "As a marketing manager, creativity is my bread and butter. Since I started using FunBlocks AIFlow, my productivity has skyrocketed. Before product strategy meetings, I use it to organize market analyses, and the AI even provides timely insights, ensuring my proposals always hit the mark. Recently, when launching a new product, I used it for brainstorming – the ideas just kept flowing, which beats staring at PowerPoint slides for hours. During cross-department communications, a single mind map speaks volumes, making discussions more focused. My boss even asked if I've been secretly taking extra courses! Well, I guess you could say I've found the ultimate work partner."}, "aiflow.testimonials.user2.name": {"message": "<PERSON>"}, "aiflow.testimonials.user2.role": {"message": "Product Manager"}, "aiflow.testimonials.user2.text": {"message": "I'm happy to recommend FunBlocks AI to everyone I meet—it's fantastic for boosting productivity and creativity. As a product manager, I handle a lot of information and ideas every day. I particularly love its infinite whiteboard feature, which allows me to freely expand my thoughts. Before product planning, I often use it to organize user needs and market trends. The AI provides timely insights, helping me focus on the key points and come up with new ideas from unexpected angles!"}, "aiflow.testimonials.user3.name": {"message": "<PERSON>"}, "aiflow.testimonials.user3.role": {"message": "Freelance Creative Writer"}, "aiflow.testimonials.user3.text": {"message": "As a freelance creative writer, I often struggled with writer's block. But with this magical tool, my creative well never runs dry. I particularly love its AI brainstorming feature, which always gives me unexpected inspiration. Plus, the one-click article generation feature has doubled my writing efficiency. Now, I'm not only taking on more projects, but the quality of my work has improved too. For anyone working in the creative field, I highly recommend FunBlocks AIFlow!"}, "aiflow.testimonials.user4.name": {"message": "<PERSON>"}, "aiflow.testimonials.user4.role": {"message": "College Student"}, "aiflow.testimonials.user4.text": {"message": "When I first started college, I often felt like I couldn't keep up, with professors breezing through so much material. Then my roommate recommended FunBlocks AIFlow. I absolutely love the infinite whiteboard feature – I can add whatever I learn to it, and the AI keeps expanding on it, making my studies so much more in-depth than before. And that's not all! Using AIFlow for brainstorming and thematic learning allows me to make interdisciplinary connections, linking knowledge from different subjects. The AI even provides real-world application examples, suddenly making everything I learn feel meaningful. It's the absolute best! 👍👍👍"}, "aiflow.testimonials.user5.name": {"message": "Sophia"}, "aiflow.testimonials.user5.role": {"message": "Researcher"}, "aiflow.testimonials.user5.text": {"message": "As a researcher, I often need to explore complex topics and generate new ideas. FunBlocks AIFlow has been a game changer for me. The AI-powered brainstorming feature helps me quickly generate comprehensive mind maps, and the diverse content support allows me to include various types of information in my research. The AI assistant provides valuable insights and suggestions, making my research process more efficient and productive."}, "aiflow.testimonials.user6.name": {"message": "<PERSON>"}, "aiflow.testimonials.user6.role": {"message": "Educator"}, "aiflow.testimonials.user6.text": {"message": "As an educator, I'm always looking for new ways to engage my students and enhance their learning experience. FunBlocks AIFlow has been a fantastic tool for this. The AI-powered brainstorming feature helps me create interactive and visually appealing learning materials. The instant work outputs feature allows me to quickly generate presentation slides and infographics, making my teaching more dynamic and engaging. My students love it, and I've seen a significant improvement in their understanding and retention of the material."}, "aiflow.cta.title": {"message": "Ready to Embark on a Knowledge Adventure?"}, "aiflow.cta.subtitle": {"message": "Join <PERSON><PERSON>s <PERSON><PERSON><PERSON> and unleash your limitless cognitive potential!"}, "aiflow.cta.button": {"message": "Start Your Free Trial Now"}, "aiflow.faq.title": {"message": "Frequently Asked Questions"}, "aiflow.faq.q1": {"message": "What is FunBlocks AIFlow?"}, "aiflow.faq.a1": {"message": "FunBlocks AIFlow is an AI-driven visual thinking platform designed to help users brainstorm and generate innovative ideas. It can transform complex concepts into mind maps, slides, and infographics, combining the powerful capabilities of AI with visual thinking techniques to significantly enhance your cognitive abilities and productivity."}, "aiflow.faq.q2": {"message": "How does AIFlow differ from ChatGPT?"}, "aiflow.faq.a2": {"message": "ChatGPT provides linear text conversations, while AIFlow offers a visual and multidimensional workspace. You can explore ideas in multiple directions simultaneously, maintain context through visual hierarchies, and instantly convert your thoughts into professional deliverables."}, "aiflow.faq.q3": {"message": "What can I create with AIFlow?"}, "aiflow.faq.a3": {"message": "With AIFlow, you can create mind maps, presentation slides, infographics, strategic plans, knowledge maps, and visual analyses of complex topics. The platform supports various thinking frameworks and allows you to convert your visual creations into different formats."}, "aiflow.faq.q4": {"message": "Which AI models are supported by AIFlow?"}, "aiflow.faq.a4": {"message": "AIFlow supports all major AI models including GPT-4, GPT-3.5, Claude 3, Claude 2, Gemini Pro, and more. You can also use your own API key to access these models through AIFlow."}, "aiflow.faq.q5": {"message": "Can I collaborate with others using AIFlow?"}, "aiflow.faq.a5": {"message": "Yes, AIFlow supports collaboration. You can share your mind maps and projects with team members or clients, allowing for real-time feedback and collaborative idea development."}, "aiflow.faq.q6": {"message": "Do I need technical skills to use AIFlow?"}, "aiflow.faq.a6": {"message": "No technical skills are required. AIFlow features an intuitive interface that allows you to easily start creating mind maps and use the AI assistant for brainstorming and exploration."}, "aiflow.faq.q7": {"message": "Can I export my mind maps and projects?"}, "aiflow.faq.a7": {"message": "Yes, AIFlow allows you to export your work in various formats including PNG, PDF, and more. You can also convert mind maps directly into presentations, documents, or other deliverables."}, "aiflow.faq.q8": {"message": "Is AIFlow just another AI mind mapping tool?"}, "aiflow.faq.a8": {"message": "AIFlow goes beyond traditional mind mapping. It offers an infinite canvas with diverse node types, proactive AI inspiration, one-click content generation, and customizable AI prompts, making it a comprehensive tool for creativity and productivity."}, "aiflow.faq.q9": {"message": "What advantages does AIFlow offer in assisting thinking?"}, "aiflow.faq.a9": {"message": "AIFlow enhances thinking through multidimensional analysis, breaking thought limitations, establishing knowledge connections, supporting classic thinking models, decomposing complex problems, and guiding knowledge exploration."}, "aiflow.faq.q10": {"message": "What advantages does using AIFlow for brainstorming offer?"}, "aiflow.faq.a10": {"message": "AIFlow simplifies brainstorming with AI technology, enabling rapid generation of comprehensive mind maps. Its flexible interface allows exploration from different angles, and it incorporates classic thinking models to help generate novel ideas."}, "aiflow.faq.q11": {"message": "Is there a free trial available?"}, "aiflow.faq.a11": {"message": "Yes, FunBlocks AIFlow offers a free trial. New users receive 30 free AI service accesses upon registration, with an additional 10 free accesses daily."}, "aiflow.faq.q12": {"message": "How can I get started with AIFlow?"}, "aiflow.faq.a12": {"message": "Simply click the 'Free Trial' button, create an account, and you can immediately start using AIFlow. No credit card is required for the trial, and you'll have access to all features."}, "modal.alt": {"message": "Enlarged view"}, "modal.click_to_close": {"message": "Click to close"}, "slides.masthead.title": {"message": "<PERSON><PERSON> Smart<PERSON>, Present Better"}, "slides.masthead.subtitle": {"message": "Leverage AI to make slide creation and presentations easy, tailored for anyone who seek excellence in content and efficient work"}, "slides.masthead.target": {"message": "Designed for professionals who seek excellence in content and efficiency"}, "slides.masthead.cta": {"message": "Generate Slides with One Click, Free Trial"}, "slides.head.title": {"message": "FunBlocks AI Slides: Efficient AI Presentation Tool | Brainstorming to Presentation in One Workspace"}, "slides.head.description": {"message": "Create stunning, content-focused presentations effortlessly with FunBlocks AI Slides. Our AI-powered tool combines brainstorming, mind mapping, and critical thinking features to help you create impactful presentations. Perfect for educators, professionals, and students."}, "slides.intro.title": {"message": "FunBlocks AI Slides"}, "slides.intro.description": {"message": "Ignite your ideas with AI-powered inspiration and one-click slides creation"}, "slides.intro.point1.name": {"message": "<PERSON><PERSON> Writing"}, "slides.intro.point1.description": {"message": "Craft professional slides instantly with intuitive Markdown syntax"}, "slides.intro.point2.name": {"message": "Minimalist Aesthetics"}, "slides.intro.point2.description": {"message": "Focus on content creation, leave behind tedious formatting"}, "slides.intro.point3.name": {"message": "Powered by AI"}, "slides.intro.point3.description": {"message": "Intelligently generate and optimize content, spark creative ideas, and offer presentation guidance"}, "slides.intro.point4.name": {"message": "Ecosystem Integration"}, "slides.intro.point4.description": {"message": "Seamlessly connect with FunBlocks documents and AIFlow, converting various content into stunning slides with a single click"}, "slides.intro.point5.name": {"message": "Cloud Collaboration"}, "slides.intro.point5.description": {"message": "Create, present, and share online, anytime, anywhere"}, "slides.features.title": {"message": "Key Features"}, "slides.features.item1.name": {"message": "<PERSON><PERSON> Writing"}, "slides.features.item1.description": {"message": "Efficiently create slides by simply inputting text, enjoying the focus and speed of full keyboard operation."}, "slides.features.item2.name": {"message": "Slash Command Menu"}, "slides.features.item2.description": {"message": "While editing, just type '/' to bring up the editing menu, making your work easy and smooth, similar to the Notion experience."}, "slides.features.item3.name": {"message": "AI-Powered Content Generation"}, "slides.features.item3.description": {"message": "Intelligently generate high-quality slide content based on your topic or enhance existing content to improve presentation impact."}, "slides.features.item4.name": {"message": "Speaker Notes"}, "slides.features.item4.description": {"message": "AI-generated speech notes help presenters convey their messages more effectively."}, "slides.features.item5.name": {"message": "Academic and Professional Support"}, "slides.features.item5.description": {"message": "Supports KaTeX typesetting (a subset of LaTeX) and code block highlighting to meet academic and professional needs."}, "slides.features.item6.name": {"message": "Online Sharing and Presentation"}, "slides.features.item6.description": {"message": "One-click sharing and online presentations, eliminating the hassle of version inconsistencies."}, "slides.features.item7.name": {"message": "Multiple Theme Options"}, "slides.features.item7.description": {"message": "Choose from a variety of simple yet professional themes to suit different scenarios, keeping your content in the spotlight."}, "slides.features.item8.name": {"message": "Presenter <PERSON>"}, "slides.features.item8.description": {"message": "Includes a timer, next slide preview, and speaker notes. Press 'S' to try it out."}, "slides.features.more": {"message": "Click to explore more features and demos"}, "slides.platform-synergy.title": {"message": "Platform Synergy: AI Slides Within the FunBlocks Ecosystem"}, "slides.platform-synergy.description": {"message": "Experience the full potential of AI Slides as an integral part of the FunBlocks AI workspace, creating a seamless knowledge workflow"}, "slides.platform-synergy.point1.name": {"message": "Integrated Resource Utilization"}, "slides.platform-synergy.point1.description": {"message": "Instantly leverage content and ideas from other FunBlocks tools to generate comprehensive, accurate slides without crafting detailed prompts"}, "slides.platform-synergy.point2.name": {"message": "Enhanced AI Generation Quality"}, "slides.platform-synergy.point2.description": {"message": "Benefit from platform-wide context awareness that produces exceptionally high-quality slide content based on your existing workspace materials"}, "slides.platform-synergy.point3.name": {"message": "Seamless Knowledge Presentation"}, "slides.platform-synergy.point3.description": {"message": "Effortlessly transform your research, writing, and brainstorming into elegant, professional presentations that communicate complex ideas clearly"}, "slides.platform-synergy.point4.name": {"message": "Complete Workflow Integration"}, "slides.platform-synergy.point4.description": {"message": "Complete your productivity cycle with presentations that naturally extend from ideation to communication, creating a cohesive AI workspace experience"}, "slides.ai-assistant.title": {"message": "AI Assistant: Your Intelligent Slide Creation Partner"}, "slides.ai-assistant.description": {"message": "Collaborate with AI to generate slides, optimize content, and refine your delivery for a truly impactful presentation"}, "slides.ai-assistant.point1.name": {"message": "One-Click Slide Generation"}, "slides.ai-assistant.point1.description": {"message": "Simply input a topic, and the AI assistant generates a complete set of slides with rich content and structure"}, "slides.ai-assistant.point2.name": {"message": "Content Optimization and Refinement"}, "slides.ai-assistant.point2.description": {"message": "AI text enhancement for clearer, more concise expression, highlighting core information"}, "slides.ai-assistant.point3.name": {"message": "Creative Perspectives and Suggestions"}, "slides.ai-assistant.point3.description": {"message": "Provide novel perspectives and angles for your presentation, enhancing persuasiveness"}, "slides.ai-assistant.point4.name": {"message": "Speech Script Generation"}, "slides.ai-assistant.point4.description": {"message": "Creates high-quality, persuasive speeches customized to each topic and includes tips for effective delivery"}, "slides.use-cases.title": {"message": "Use Cases"}, "slides.use-cases.description": {"message": "AI Slides has extensive and innovative applications across various fields"}, "slides.use-cases.point1.name": {"message": "Academic Reports"}, "slides.use-cases.point1.description": {"message": "Present research findings and academic viewpoints clearly and concisely, making complex content easier for audiences to understand"}, "slides.use-cases.point2.name": {"message": "Business Presentations"}, "slides.use-cases.point2.description": {"message": "Quickly create professional business reports and proposals, enhancing work efficiency and providing presentation guidance"}, "slides.use-cases.point3.name": {"message": "Course Teaching"}, "slides.use-cases.point3.description": {"message": "Easily create engaging teaching slides to enhance student engagement"}, "slides.use-cases.point4.name": {"message": "Personal Sharing"}, "slides.use-cases.point4.description": {"message": "Craft compelling presentations and speeches for personal ideas and stories, improving presentation skills"}, "slides.use-cases.point5.name": {"message": "Team Sharing"}, "slides.use-cases.point5.description": {"message": "Efficiently share information, keep teams synchronized, and improve collaboration efficiency"}, "slides.use-cases.point6.name": {"message": "Quick Briefings"}, "slides.use-cases.point6.description": {"message": "Ideal for scenarios requiring rapid creation of concise and power points without spending extensive time on complex designs"}, "slides.testimonials.user1.name": {"message": "<PERSON>"}, "slides.testimonials.user1.role": {"message": "Company Employee"}, "slides.testimonials.user1.text": {"message": "As an ordinary company employee, <PERSON><PERSON><PERSON>s AI Slides' AI assistant has completely transformed how I prepare work reports. It helps me generate and optimize slides, making my content clearer and more logical. What surprised me the most is how the AI assistant expanded my thinking, making my proposals more innovative and practical, which has helped me stand out in front of my superiors. Additionally, it provides presentation guidance that has significantly improved my communication skills. Now, I express my ideas with greater confidence within the team, and this progress has greatly benefited my career development. AI Slides is not just a tool; it's more like a booster for my professional growth."}, "slides.testimonials.user2.name": {"message": "<PERSON>"}, "slides.testimonials.user2.role": {"message": "Mid-level Corporate Manager"}, "slides.testimonials.user2.text": {"message": "As a mid-level manager, I report on business and work progress weekly. AIFlow simplifies this process, helping me quickly distill core content into concise presentations. It also generates speaker notes, making my reports more organized. Previously, I spent a lot of time organizing materials, but now I can complete a professional report in a short time. AIFlow greatly improves my work efficiency and is an invaluable tool for professionals."}, "slides.testimonials.user3.name": {"message": "<PERSON>"}, "slides.testimonials.user3.role": {"message": "University Professor"}, "slides.testimonials.user3.text": {"message": "FunBlocks AI Slides is one of the best teaching tools I've used. As a computer science professor, I often need to create complex slides with code and technical concepts. This tool's Markdown support allows me to easily insert code, while the <PERSON> assistant helps transform complex content into explanations students can easily understand. Most impressively, it can automatically generate complete slides based on course outlines, allowing me to focus more on teaching itself. FunBlocks AI Slides is an indispensable tool for educators."}, "slides.testimonials.user4.name": {"message": "<PERSON>"}, "slides.testimonials.user4.role": {"message": "Marketing Strategy Manager"}, "slides.testimonials.user4.text": {"message": "In marketing, speed and creativity are equally important. FunBlocks AI Slides perfectly meets both needs. Its AI assistant is like my personal creative consultant, always providing new inspiration when I need it. On the FunBlocks platform, I usually start with AIFlow for brainstorming, getting new ideas and solutions, then quickly generate slides. This workflow is both efficient and creative. FunBlocks is a must-have tool for marketing strategists."}, "slides.testimonials.user5.name": {"message": "<PERSON>"}, "slides.testimonials.user5.role": {"message": "Graduate Student"}, "slides.testimonials.user5.text": {"message": "As a graduate student, I often need to prepare academic reports and defenses. FunBlocks AI Slides makes this much easier. It supports Markdown, allowing easy insertion of complex mathematical formulas and citations, while the AI assistant optimizes my expression, making my presentations clearer and more persuasive. The minimalist design style lets me focus on the content itself rather than wasting time on formatting. FunBlocks AI Slides is definitely a powerful tool for every student and scholar, making your academic work twice as efficient."}, "slides.testimonials.user6.name": {"message": "<PERSON>"}, "slides.testimonials.user6.role": {"message": "Freelance Designer"}, "slides.testimonials.user6.text": {"message": "As a freelance designer, I often need to present my design concepts to clients. FunBlocks AI Slides has been a game changer for me. It allows me to quickly and efficiently create professional presentations, focusing on the content rather than the design. The AI assistant helps me generate and optimize content, making my presentations more impactful. It's a tool that every designer should have in their arsenal."}, "slides.cta.title": {"message": "Ready to experience the innovative AI slides?"}, "slides.cta.subtitle": {"message": "Join <PERSON><PERSON>s AI Slides and create outstanding presentations with ease!"}, "slides.cta.button": {"message": "Start Your Free Trial Now"}, "slides.faq.title": {"message": "Frequently Asked Questions"}, "slides.faq.q1": {"message": "I'm not familiar with Markdown syntax. Can I still use FunBlocks AI Slides?"}, "slides.faq.a1": {"message": "Absolutely! FunBlocks AI Slides provides a simple Markdown guide to help you get started quickly. When editing, just type '/' to bring up the editing menu, similar to how it works in Notion—it's super convenient. Plus, our AI assistant can help you generate and optimize content, so you don't need to worry about syntax. We want you to focus on your content, not the tools."}, "slides.faq.q2": {"message": "How does FunBlocks AI Slides improve my work efficiency?"}, "slides.faq.a2": {"message": "FunBlocks AI Slides helps you save significant time by simplifying the slide creation process. Features like AI content generation, one-click theme application, and full keyboard operation are all designed to help you complete high-quality presentations faster. You can devote more energy to the content itself rather than spending a lot of time on formatting."}, "slides.faq.q3": {"message": "Who is FunBlocks AI Slides suitable for?"}, "slides.faq.a3": {"message": "FunBlocks AI Slides is especially suitable for professionals who value efficiency and prioritize content quality over form. Whether you're an academic, educator, business manager, or freelancer, if you want to quickly create high-quality, professional presentations, FunBlocks AI Slides is your ideal choice."}, "slides.faq.q4": {"message": "Do presentations made with FunBlocks AI Slides look too simple?"}, "slides.faq.a4": {"message": "Not at all. While we emphasize simplicity and efficiency, the themes and designs provided by FunBlocks AI Slides are carefully selected to ensure they are both professional and aesthetically pleasing. Our philosophy is 'content is king,' helping you convey core information through clear, powerful presentations, rather than relying on flashy but potentially distracting design elements."}, "slides.faq.q5": {"message": "How does FunBlocks AI Slides protect my data and privacy?"}, "slides.faq.a5": {"message": "We take user data security and privacy very seriously. All data is encrypted in storage, and we do not use your content for other purposes. The use of AI features also follows strict privacy protection guidelines. You can use FunBlocks AI Slides with peace of mind, focusing on creating excellent presentation content."}, "slides.faq.q6": {"message": "What advantages does FunBlocks AI Slides have compared to traditional PPT software?"}, "slides.faq.a6": {"message": "FunBlocks AI Slides focuses on improving efficiency and content quality. Key advantages include: \n1. Quick creation based on Markdown; \n2. AI-assisted content generation and optimization; \n3. Minimalist design, reducing time spent on formatting; \n4. Online collaboration and sharing; \n5. Seamless integration with the FunBlocks ecosystem. \nThese features allow you to create high-quality presentations faster, rather than getting bogged down in complex design details."}, "slides.faq.q7": {"message": "Can I import existing PPTs into FunBlocks AI Slides?"}, "slides.faq.a7": {"message": "Currently, FunBlocks AI Slides doesn't directly support importing traditional PPT files. However, our AI assistant can help you quickly rebuild existing content. Simply paste the main content of your PPT into our editor, and AI will help you reorganize and optimize the content, creating a new, more efficient version of your presentation."}, "slides.faq.q8": {"message": "Is it true that Amazon does not allow the use of PPT for internal meetings?"}, "slides.faq.a8": {"message": "Yes, it's true. Founder <PERSON> believes that PPT is too brief and can cause key details to be overlooked, which hinders information transfer and decision-making. Therefore, the company prohibits its use and instead uses a '6-page document.' \nHowever, FunBlocks AI Slides addresses this issue with AI enhanced Speaker Notes, allowing the AI assistant to generate detailed speaking content as speaker notes, which helps both the speaker and provides sufficient detail for the audience."}, "slides.faq.q9": {"message": "Are there copyright issues with AI-generated content?"}, "slides.faq.a9": {"message": "The AI-generated content in FunBlocks AI Slides is original content based on the keywords and topics you provide. The copyright of this content belongs to you. However, we recommend that you still review the AI-generated content to ensure its accuracy and applicability. For any quotes or specific data, we suggest you provide appropriate citations and verification."}, "slides.faq.q10": {"message": "Is FunBlocks AI Slides suitable for creating complex tech or data presentations?"}, "slides.faq.a10": {"message": "Absolutely. While FunBlocks AI Slides emphasizes simplicity, it fully supports complex tech and data presentations. You can easily insert code blocks, mathematical formulas, charts, and images. Our AI assistant can also help explain complex concepts, making your presentation easier for audiences to understand."}, "slides.faq.q11": {"message": "Can I customize the themes and styles in FunBlocks AI Slides?"}, "slides.faq.a11": {"message": "Yes, FunBlocks AI Slides offers multiple preset themes and also supports customization. You can adjust colors, fonts, and layouts to match your brand style. But remember, our design philosophy is to keep things simple and let the content be the star."}, "slides.faq.q12": {"message": "How does FunBlocks AI Slides handle images and multimedia content?"}, "slides.faq.a12": {"message": "While FunBlocks AI Slides is primarily text-based, it fully supports the insertion of images, videos, and other multimedia content. You can add these elements through simple Markdown syntax or slash-menu operations. The AI assistant can also help you select or generate image suggestions related to your content."}, "slides.faq.q13": {"message": "Can I use FunBlocks AI Slides offline?"}, "slides.faq.a13": {"message": "FunBlocks AI Slides is primarily an online tool, but we support downloading presentations as PDF versions."}, "slides.faq.q14": {"message": "Does FunBlocks AI Slides support presenter mode and timing functions?"}, "slides.faq.a14": {"message": "Yes, FunBlocks AI Slides provides a professional presenter mode. You can view the next slide, speaker notes, and use the built-in timer to control your presentation pace. These features are optimized to ensure you remain professional and composed during your presentation."}, "slides.faq.q15": {"message": "How do I start using FunBlocks AI Slides? Is it beginner-friendly?"}, "slides.faq.a15": {"message": "FunBlocks AI Slides is very welcoming to beginners! We provide detailed beginner guides and interactive tutorials to help you get started quickly. From the moment you start using it, our AI assistant will guide you throughout the process, answering questions and providing suggestions. You'll find that creating your first professional presentation is simpler and quicker than you imagined."}, "slides.faq.q16": {"message": "Can I use FunBlocks AI Slides for free?"}, "slides.faq.a16": {"message": "Yes, FunBlocks AI offers a free usage option. New users will receive 30 free AI service accesses upon registration, along with an additional 10 free accesses daily, ensuring you can experience the powerful features of the AI assistant. Additionally, FunBlocks AI has a rewards mechanism; by inviting friends to register, both you and your friends can receive free usage credits."}, "slides.faq.q17": {"message": "How can I earn free usage credits by inviting friends?"}, "slides.faq.a17": {"message": "You can invite friends in two ways. First, share the invitation link; when your friend registers through the link, they will receive a reward. Second, share content generated by the FunBlocks AI browser extension or AI Flow; if users click to read and register, you will also receive an invitation reward."}, "slides.faq.q18": {"message": "How to cancel a subscribed plan?"}, "slides.faq.a18": {"message": "You can find the 'Upgrade AI' section in the settings. If you are subscribed to a membership plan, there will be a Cancel button in the corresponding description area; click it to unsubscribe."}, "slides.comparison.title": {"message": "How FunBlocks AI Slides Compares"}, "slides.comparison.description": {"message": "FunBlocks AI Slides offers a unique approach to presentation creation by integrating brainstorming, mind mapping, and critical thinking tools in one seamless workspace"}, "slides.comparison.funblocksHeader": {"message": "FunBlocks AI Slides"}, "slides.comparison.powerPointHeader": {"message": "PowerPoint"}, "slides.comparison.googleSlidesHeader": {"message": "Google Slides"}, "slides.comparison.canvaHeader": {"message": "<PERSON><PERSON>"}, "slides.comparison.feature1": {"message": "AI-Powered Content Generation"}, "slides.comparison.feature2": {"message": "Integrated Brainstorming Tools"}, "slides.comparison.feature3": {"message": "Mind Mapping Integration"}, "slides.comparison.feature4": {"message": "Critical Thinking Enhancement"}, "slides.comparison.feature5": {"message": "Markdown-Based Creation"}, "slides.comparison.feature6": {"message": "Speaker Notes Generation"}, "slides.comparison.feature7": {"message": "Distraction-Free Interface"}, "slides.comparison.feature8": {"message": "One-Click Conversion from Mind Maps"}, "slides.comparison.note": {"message": "FunBlocks AI Slides focuses on enhancing your thinking process from brainstorming to presentation, helping you create more impactful, well-structured content rather than just focusing on design elements."}, "slides.thinking_process.title": {"message": "From Brainstorming to Presentation: The Complete Thinking Process"}, "slides.thinking_process.description": {"message": "FunBlocks AI provides a seamless journey from initial ideas to polished presentations, enhancing both critical and creative thinking along the way"}, "slides.thinking_process.step1.title": {"message": "Brainstorming with AI"}, "slides.thinking_process.step1.description": {"message": "Generate ideas and explore concepts using AI-powered brainstorming tools that help expand your thinking and uncover new perspectives"}, "slides.thinking_process.step2.title": {"message": "Organize with Mind Mapping"}, "slides.thinking_process.step2.description": {"message": "Structure your thoughts visually using our intuitive mind mapping tools, creating logical connections between ideas"}, "slides.thinking_process.step3.title": {"message": "Apply Critical Thinking"}, "slides.thinking_process.step3.description": {"message": "Analyze and refine your ideas with critical thinking tools that help identify strengths, weaknesses, and opportunities for improvement"}, "slides.thinking_process.step4.title": {"message": "Create Impactful Slides"}, "slides.thinking_process.step4.description": {"message": "Transform your organized thoughts into compelling slides with one click, maintaining the logical structure of your thinking process"}, "extension_welcome.head.title": {"message": "FunBlocks AI Browser Extension - AI-Powered Brainstorming, Mindmapping & Critical Thinking Tools | Boost Productivity"}, "extension_welcome.head.description": {"message": "Transform your browsing with FunBlocks AI Extension. Features AI-powered brainstorming, mindmapping, critical thinking frameworks (Six Thinking Hats, SWOT Analysis), and creative writing tools. Generate infographics, insight cards, and visual mind maps. Compatible with <PERSON><PERSON>GP<PERSON>, <PERSON>, Gemini Pro. Enhance how you read, write, and think online."}, "extension_welcome.hero.badge": {"message": "🚀 AI-Powered Browser Extension"}, "extension_welcome.hero.title": {"message": "Transform Your Browsing with FunBlocks AI Assistant"}, "extension_welcome.hero.subtitle": {"message": "Your intelligent companion for AI-powered reading, writing, brainstorming, and critical thinking across the web"}, "extension_welcome.hero.cta_primary": {"message": "Install Free Extension"}, "extension_welcome.hero.cta_secondary": {"message": "Explore Features"}, "extension_welcome.hero.feature1": {"message": "AI Brainstorming"}, "extension_welcome.hero.feature2": {"message": "Critical Thinking"}, "extension_welcome.hero.feature3": {"message": "Smart Writing"}, "extension_welcome.hero.image_overlay": {"message": "Click to view full interface"}, "extension_welcome.benefits.title": {"message": "Key Benefits"}, "extension_welcome.benefits.description": {"message": "Discover how FunBlocks AI Extension transforms your browsing experience with these powerful benefits:"}, "extension_welcome.benefits.benefit1.title": {"message": "Enhanced Productivity"}, "extension_welcome.benefits.benefit1.description": {"message": "Save time and work more efficiently with AI-powered tools that streamline your reading, writing, and thinking processes."}, "extension_welcome.benefits.benefit2.title": {"message": "Improved Critical Thinking"}, "extension_welcome.benefits.benefit2.description": {"message": "Develop stronger analytical skills with structured thinking frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking."}, "extension_welcome.benefits.benefit3.title": {"message": "Boosted Creativity"}, "extension_welcome.benefits.benefit3.description": {"message": "Unlock new ideas and perspectives with AI-powered brainstorming and mindmapping tools that expand your creative horizons."}, "extension_welcome.benefits.benefit4.title": {"message": "Seamless Integration"}, "extension_welcome.benefits.benefit4.description": {"message": "Works across all websites with context-aware tools that understand what you're doing and provide relevant assistance."}, "extension_welcome.benefits.benefit5.title": {"message": "Visual Learning"}, "extension_welcome.benefits.benefit5.description": {"message": "Transform complex information into visual formats like mindmaps, infographics, and insight cards for better understanding and retention."}, "extension_welcome.benefits.benefit6.title": {"message": "Privacy & Control"}, "extension_welcome.benefits.benefit6.description": {"message": "Choose your preferred AI models and maintain control over your data with flexible privacy options."}, "extension_welcome.nav.home": {"message": "<PERSON><PERSON><PERSON><PERSON> - AI Assistant"}, "extension_welcome.nav.settings": {"message": "Customize"}, "extension_welcome.nav.reading": {"message": "AI Reader"}, "extension_welcome.nav.writing": {"message": "AI Docs"}, "extension_welcome.nav.contextual": {"message": "AI Tools"}, "extension_welcome.nav.pricing": {"message": "Pricing"}, "extension_welcome.nav.login": {"message": "<PERSON><PERSON>"}, "extension_welcome.pin.title": {"message": "Enhance Your Browsing with FunBlocks AI Assistant"}, "extension_welcome.pin.desc": {"message": "Your AI-powered reading, writing, and thinking companion for the web"}, "extension_welcome.pin.li1": {"message": "Click the extension icon in your browser's top-right corner"}, "extension_welcome.pin.li2": {"message": "Pin FunBlocks AI to your toolbar for instant access"}, "extension_welcome.pin.li3": {"message": "Your AI assistant is now ready to help anytime"}, "extension_welcome.pin.more": {"message": "Discover powerful AI features for reading, writing, and critical thinking below ⬇️"}, "extension_welcome.settings.title": {"message": "Personalize Your AI Assistant and Boost Your Productivity"}, "extension_welcome.settings.subtitle": {"message": "With a wide range of customizable options, you can easily tailor your AI assistant to your needs"}, "extension_welcome.settings.inbox_llms.title": {"message": "Unlock Premium LLM Models with a Single Login"}, "extension_welcome.settings.inbox_llms.li1": {"message": "Access top AI models like ChatGPT-4o, Claude-3.7, Gemini-2.5-Pro, DeepSeek, and more"}, "extension_welcome.settings.inbox_llms.li2": {"message": "All the Leading AI Models in One Place, seamlessly integrated into your workflow"}, "extension_welcome.settings.inbox_llms.li3": {"message": "Enjoy a cost-effective solution with a single payment, eliminating the need for individual model subscriptions"}, "extension_welcome.settings.private_llms.title": {"message": "Or Use Your Own LLM API Key"}, "extension_welcome.settings.private_llms.li1": {"message": "ChatGPT, Claude, Gemini serials, or OpenAI Compatible APIs"}, "extension_welcome.settings.private_llms.li2": {"message": "Enjoy all the AI features for free with your own API key"}, "extension_welcome.reading.title": {"message": "AI-Enhanced Reading & Critical Thinking"}, "extension_welcome.reading.subtitle": {"message": "Transform how you read and analyze content with AI-powered critical thinking tools"}, "extension_welcome.reading.desc": {"message": "Look right 👉, hover over the FunBlocks AI icon to access:"}, "extension_welcome.reading.li1": {"message": "Page Assistant: Quickly understand and analyze current page content"}, "extension_welcome.reading.li2": {"message": "AI Assistant: Unlimited AI interaction and task processing"}, "extension_welcome.reading.li3": {"message": "AI Flow: Explore topics in-depth with visualized mind maps and unlimited canvas"}, "extension_welcome.reading.li4": {"message": "AI Screenshot: AI analysis of any screen area"}, "extension_welcome.reading.critical_thinking": {"message": "FunBlocks AI not only speeds up reading but also enhances critical thinking:"}, "extension_welcome.reading.ct1": {"message": "Extract key information, summarize main points"}, "extension_welcome.reading.ct2": {"message": "Expand related knowledge, deepen understanding"}, "extension_welcome.reading.ct3": {"message": "Identify potential biases, distinguish facts from opinions"}, "extension_welcome.contextual.title": {"message": "Contextual AI Tools & Smart Widgets"}, "extension_welcome.contextual.subtitle": {"message": "The FunBlocks AI Extension uses page or selected content as context to perform AI tasks, eliminating the need for copy-pasting between apps"}, "extension_welcome.contextual.toolbar": {"message": "Select any text to instantly bring up the AI toolbar"}, "extension_welcome.contextual.toolbar_desc": {"message": "Translate, explain, polish, and continue writing with one click - AI assistant always at the ready for any selected text"}, "extension_welcome.contextual.try_now": {"message": "Try it now, select the text below:"}, "extension_welcome.contextual.select_text": {"message": "FunBlocks AI: Your all-in-one smart reading and writing assistant with brainstorming and critical thinking tools"}, "extension_welcome.contextual.widget": {"message": "Intelligent Context-Aware Widgets"}, "extension_welcome.contextual.widget_desc": {"message": "Smart widgets automatically appear when needed - email assistant for Gmail, video summarizer for YouTube, and more specialized tools across the web"}, "extension_welcome.writing.title": {"message": "AI-Powered Writing & Content Creation"}, "extension_welcome.writing.subtitle": {"message": "Experience AI-assisted writing in the editor box below"}, "extension_welcome.writing.methods": {"message": "Multiple Ways to Access AI Writing Tools:"}, "extension_welcome.writing.li1": {"message": "Select text to activate the smart contextual toolbar"}, "extension_welcome.writing.li2": {"message": "Click the FunBlocks AI icon in the bottom right"}, "extension_welcome.writing.li3": {"message": "Type '/' for quick command access to all writing tools"}, "extension_welcome.writing.features": {"message": "Advanced Writing Capabilities:"}, "extension_welcome.writing.f1": {"message": "One-click topic-based article generation"}, "extension_welcome.writing.f2": {"message": "Professional text polishing with tone and style adjustment"}, "extension_welcome.writing.f3": {"message": "Smart content expansion with brainstorming capabilities"}, "extension_welcome.writing.f4": {"message": "Context-aware responses for emails, social media, and forums"}, "extension_welcome.differentiation.title": {"message": "Beyond Basic AI Assistance: Cultivating Higher-Order Thinking"}, "extension_welcome.differentiation.subtitle": {"message": "While other browser AI assistants focus on task completion, FunBlocks AI Assistant prioritizes developing your critical thinking and cognitive abilities"}, "extension_welcome.differentiation.funblocks_title": {"message": "FunBlocks AI Assistant"}, "extension_welcome.differentiation.funblocks_focus": {"message": "Focus: Enhancing Human Intelligence"}, "extension_welcome.differentiation.funblocks_feature1": {"message": "Visual mind mapping for comprehensive information presentation"}, "extension_welcome.differentiation.funblocks_feature2": {"message": "Multi-perspective exploration to avoid information silos"}, "extension_welcome.differentiation.funblocks_feature3": {"message": "Critical analysis tools: bias detection, counterexamples, comprehensive analysis"}, "extension_welcome.differentiation.funblocks_feature4": {"message": "Structured thinking frameworks (Six Thinking Hats, SWOT, First Principles)"}, "extension_welcome.differentiation.others_title": {"message": "Traditional AI Assistants (<PERSON>, etc.)"}, "extension_welcome.differentiation.others_focus": {"message": "Focus: Task Completion"}, "extension_welcome.differentiation.others_limitation1": {"message": "Linear text responses without visual structure"}, "extension_welcome.differentiation.others_limitation2": {"message": "Single-perspective answers that may create echo chambers"}, "extension_welcome.differentiation.others_limitation3": {"message": "Limited critical thinking support and bias awareness"}, "extension_welcome.differentiation.others_limitation4": {"message": "Focus on quick answers rather than thinking development"}, "extension_welcome.differentiation.philosophy_title": {"message": "Our Philosophy: AI Enhances Human Intelligence, Not Replaces It"}, "extension_welcome.differentiation.philosophy_description": {"message": "FunBlocks AI Assistant is designed to strengthen your cognitive abilities, helping you think more critically, explore topics more comprehensively, and develop better analytical skills for the AI era."}, "extension_welcome.mindmap_demo.title": {"message": "One-Click Web Page to Mindmap Transformation"}, "extension_welcome.mindmap_demo.subtitle": {"message": "Transform any web page into a comprehensive mindmap instantly with AI-powered analysis"}, "extension_welcome.mindmap_demo.benefits_title": {"message": "Key Benefits of Mindmap Visualization"}, "extension_welcome.mindmap_demo.benefit1": {"message": "Instant visual comprehension of complex information"}, "extension_welcome.mindmap_demo.benefit2": {"message": "Hierarchical structure reveals key relationships and connections"}, "extension_welcome.mindmap_demo.benefit3": {"message": "Enhanced memory retention through visual learning"}, "extension_welcome.mindmap_demo.benefit4": {"message": "Quick identification of main topics and supporting details"}, "extension_welcome.mindmap_demo.features_title": {"message": "Advanced Mindmap Features"}, "extension_welcome.mindmap_demo.feature1": {"message": "AI-powered content analysis and categorization"}, "extension_welcome.mindmap_demo.feature2": {"message": "Interactive nodes with expandable details"}, "extension_welcome.mindmap_demo.feature3": {"message": "Export to multiple formats for sharing and collaboration"}, "extension_welcome.mindmap_demo.feature4": {"message": "Integration with brainstorming and critical thinking tools"}, "extension_welcome.useCases.title": {"message": "Use Cases"}, "extension_welcome.useCases.description": {"message": "FunBlocks AI adapts to diverse scenarios, enhancing your productivity and creativity across different contexts."}, "extension_welcome.useCases.case1.title": {"message": "For Students"}, "extension_welcome.useCases.case1.description": {"message": "Enhance learning with AI-powered note-taking, critical analysis of study materials, and brainstorming for assignments and projects."}, "extension_welcome.useCases.case1.benefit1": {"message": "Transform complex readings into visual mind maps"}, "extension_welcome.useCases.case1.benefit2": {"message": "Generate structured study notes with critical thinking frameworks"}, "extension_welcome.useCases.case1.benefit3": {"message": "Brainstorm essay topics and outline arguments"}, "extension_welcome.useCases.case2.title": {"message": "For Professionals"}, "extension_welcome.useCases.case2.description": {"message": "Boost productivity with AI assistance for emails, reports, and research. Generate professional content and analyze information efficiently."}, "extension_welcome.useCases.case2.benefit1": {"message": "Draft professional emails and responses in seconds"}, "extension_welcome.useCases.case2.benefit2": {"message": "Transform meeting notes into actionable summaries"}, "extension_welcome.useCases.case2.benefit3": {"message": "Create presentation slides from any web content"}, "extension_welcome.useCases.case3.title": {"message": "For Writers & Content Creators"}, "extension_welcome.useCases.case3.description": {"message": "Enhance creativity and productivity with AI-powered brainstorming, research assistance, and content polishing tools."}, "extension_welcome.useCases.case3.benefit1": {"message": "Generate creative content ideas with AI brainstorming"}, "extension_welcome.useCases.case3.benefit2": {"message": "Research topics deeply with critical thinking frameworks"}, "extension_welcome.useCases.case3.benefit3": {"message": "Create visual infographics and insight cards"}, "extension_welcome.useCases.case4.title": {"message": "For Researchers"}, "extension_welcome.useCases.case4.description": {"message": "Accelerate research with AI-powered analysis, structured thinking frameworks, and visual organization of complex information."}, "extension_welcome.useCases.case4.benefit1": {"message": "Analyze research papers with critical thinking tools"}, "extension_welcome.useCases.case4.benefit2": {"message": "Organize complex information with mind maps"}, "extension_welcome.useCases.case4.benefit3": {"message": "Generate research summaries and visual abstracts"}, "extension_welcome.comparison.title": {"message": "How FunBlocks AI Compares"}, "extension_welcome.comparison.description": {"message": "See how FunBlocks AI Extension stands out with its unique combination of AI-powered reading, writing, brainstorming, mindmapping, and critical thinking features."}, "extension_welcome.comparison.featureHeader": {"message": "Feature"}, "extension_welcome.comparison.funblocksHeader": {"message": "FunBlocks AI"}, "extension_welcome.comparison.chatgptHeader": {"message": "<PERSON>"}, "extension_welcome.comparison.grammarlyHeader": {"message": "Grammarly"}, "extension_welcome.comparison.otherHeader": {"message": "Other AI Extensions"}, "extension_welcome.comparison.feature1": {"message": "AI-Powered Brainstorming"}, "extension_welcome.comparison.feature2": {"message": "Visual Mind Mapping"}, "extension_welcome.comparison.feature3": {"message": "Critical Thinking Frameworks"}, "extension_welcome.comparison.feature4": {"message": "Contextual AI Toolbar"}, "extension_welcome.comparison.feature5": {"message": "Infographic & Insight Card Creation"}, "extension_welcome.comparison.feature6": {"message": "Multi-Model AI Support"}, "extension_welcome.comparison.feature7": {"message": "Context-Aware Widgets"}, "extension_welcome.comparison.feature8": {"message": "Free Version Available"}, "extension_welcome.comparison.note": {"message": "FunBlocks Browser Extension enhances your daily online reading and writing experience with AI-powered writing, reading, and critical thinking tools. It offers a unique combination of brainstorming, mind mapping, and critical thinking features to help you read, write, and think more effectively online. Unlike other extensions, FunBlocks provides a comprehensive suite of cognitive enhancement tools, going beyond basic AI assistance."}, "extension_welcome.video.title": {"message": "See FunBlocks AI in Action"}, "extension_welcome.video.description": {"message": "Watch how FunBlocks AI Extension enhances your browsing experience with AI-powered reading, writing, and critical thinking tools."}, "extension_welcome.video.feature1.title": {"message": "AI Writing Assistant"}, "extension_welcome.video.feature1.description": {"message": "Enhance your writing with AI-powered tools for content creation, editing, and brainstorming"}, "extension_welcome.video.feature2.title": {"message": "AI Reading Assistant"}, "extension_welcome.video.feature2.description": {"message": "Transform how you read online with AI-powered summarization, analysis, and visualization"}, "extension_welcome.video.feature3.title": {"message": "Cognitive Boosting Tools"}, "extension_welcome.video.feature3.description": {"message": "Elevate your thinking with structured frameworks, mindmapping, and critical analysis tools"}, "extension_welcome.cta.title": {"message": "Haven't installed FunBlocks AI Extension yet?"}, "extension_welcome.cta.subtitle": {"message": "You're missing out on a world of AI-powered productivity and creativity!"}, "extension_welcome.cta.button": {"message": "Install now for free!"}, "extension_welcome.testimonials.user1.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user1.role": {"message": "Content Strategist"}, "extension_welcome.testimonials.user1.text": {"message": "The FunBlocks AI Extension has transformed my online workflow completely. The AI writing assistant helps me draft and polish content directly on any website, while the critical thinking tools help me analyze information more deeply. I especially love how the contextual toolbar appears exactly when I need it, offering relevant AI assistance based on what I'm doing. The ability to create visual mind maps from complex articles with one click has been game-changing for content planning. This extension isn't just a tool—it's like having an entire AI workspace that follows me across the web."}, "extension_welcome.testimonials.user2.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user2.role": {"message": "Research Analyst"}, "extension_welcome.testimonials.user2.text": {"message": "As a research analyst, I need to process vast amounts of information quickly. The FunBlocks AI Extension's page assistant feature lets me instantly extract key insights from industry reports and research papers. The critical thinking frameworks help me identify potential biases and logical fallacies in the content I'm reviewing. What sets this extension apart is the seamless integration with AI Flow for deeper analysis—I can transform complex data into visual mind maps that reveal connections I might have missed. Being able to choose between different AI models (ChatGPT, Claude, Gemini, Groq) for different tasks is incredibly valuable for specialized research needs."}, "extension_welcome.testimonials.user3.name": {"message": "Sophia"}, "extension_welcome.testimonials.user3.role": {"message": "Graduate Student"}, "extension_welcome.testimonials.user3.text": {"message": "The FunBlocks AI Extension has completely changed how I approach my studies. The AI reading assistant helps me understand complex academic papers by providing summaries and explanations of difficult concepts. I use the brainstorming tools to explore research topics from multiple angles, which has significantly improved the quality of my thesis work. The mind mapping feature helps me organize my thoughts visually, making connections between different theories and concepts. What I appreciate most is how it enhances my critical thinking skills—it doesn't just give me answers, but helps me develop deeper analytical abilities that my professors have noticed and praised."}, "extension_welcome.testimonials.user4.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user4.role": {"message": "Digital Marketer"}, "extension_welcome.testimonials.user4.text": {"message": "The FunBlocks AI Extension is my secret weapon for creating engaging content across multiple platforms. The AI widgets make generating email responses, social media posts, and ad copy incredibly efficient. I'm particularly impressed with the infographics and insight cards feature—I can select text on any webpage and instantly create visual content that drives engagement. The extension's ability to understand context means the AI suggestions are always relevant to what I'm working on. Since installing this extension, I've cut my content creation time in half while actually improving quality. The combination of creative thinking tools and practical AI assistance is unmatched."}, "extension_welcome.testimonials.user5.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user5.role": {"message": "Language Teacher"}, "extension_welcome.testimonials.user5.text": {"message": "I regularly use the FunBlocks AI Extension to enhance my teaching materials and help students understand complex texts. The translation and explanation tools are invaluable for language learning, allowing students to grasp difficult concepts in context. What makes this extension special is how it promotes critical thinking rather than just providing answers. I can select passages and have the AI create thought-provoking questions or generate visual concept maps that help students see relationships between ideas. The ability to save generated content to Memo for later review creates a valuable resource library for both me and my students."}, "extension_welcome.testimonials.user6.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user6.role": {"message": "Startup Founder"}, "extension_welcome.testimonials.user6.text": {"message": "Running a startup means wearing many hats, and the FunBlocks AI Extension helps me excel in all of them. I use it for everything from drafting investor emails to analyzing competitor websites. The brainstorming and mind mapping tools have been essential for product development, helping us explore ideas from multiple angles. What impresses me most is the flexibility—I can use my own API keys for different AI models depending on the task, which gives me enterprise-level AI capabilities right in my browser. The extension's context-aware features mean I spend less time explaining what I need and more time implementing great ideas. It's like having an entire AI department at my fingertips."}, "extension_welcome.faq.q1": {"message": "If I use my own API Key for LLMs (ChatGPT, Claude, Gemini, Groq), do I need to pay for FunBlocks AI services?"}, "extension_welcome.faq.a1": {"message": "No, if you use your own API Key, you only need to pay the service provider. There are no additional charges from FunBlocks AI, and you are not limited by the daily free access quota."}, "extension_welcome.faq.q2": {"message": "Can I use the AI assistant without an API Key for LLMs?"}, "extension_welcome.faq.a2": {"message": "Yes, after registering for FunBlocks, you get 40 free AI service accesses, with an additional 10 free accesses each day. You can also join the FunBlocks AI membership for unlimited AI services."}, "extension_welcome.faq.q3": {"message": "FunBlocks AI has many products. Are they complicated and hard to learn?"}, "extension_welcome.faq.a3": {"message": "FunBlocks AI offers multiple products tailored to different knowledge work scenarios. These products can work together or independently, so you can choose what suits you best without needing to learn everything. FunBlocks AI is designed to be simple and user-friendly. We recommend starting with the browser extension/plugin, which can be installed with one click."}, "extension_welcome.faq.q4": {"message": "Why is the browser extension emphasized?"}, "extension_welcome.faq.a4": {"message": "The browser extension integrates the AI assistant into all web applications, not just the FunBlocks platform. This allows you to use it anytime without copying and pasting. The plugin also supports contextual features like one-click email replies, comment responses, and page content summaries, significantly boosting your productivity."}, "extension_welcome.faq.q5": {"message": "Why does FunBlocks AI offer so many products?"}, "extension_welcome.faq.a5": {"message": "FunBlocks AI designs products for different scenarios to enhance efficiency. By leveraging large language models (LLMs), we solve user problems directly within their workflows, simplifying tasks. For example, one-click AI email replies and context-specific content generation are more efficient than using ChatGPT or Claude."}, "extension_welcome.faq.q8": {"message": "Why should I use FunBlocks AI if I already have ChatGPT?"}, "extension_welcome.faq.a8": {"message": "FunBlocks AI offers several advantages over using ChatGPT directly. Our products integrate deeply into your workflow, automatically fetching task context and combining optimized prompts. The results from LLMs are directly applied to your tasks, providing great convenience. You don't need to learn how to optimize prompts or interact with LLMs yourself. Additionally, FunBlocks AI Flow offers innovative multi-angle, multi-threaded, and visual interactions with LLMs, sparking creativity and deeper thinking, which ChatGPT alone may not provide."}, "extension_welcome.faq.q10": {"message": "How does FunBlocks differ from Notion?"}, "extension_welcome.faq.a10": {"message": "FunBlocks focuses more on AI, with browser plugins supporting all web applications, user-defined AI prompt applications, and AI app store. It supports all major third-party large language model API (ChatGPT, Claude, Gemini, Groq), giving users model choice and enabling 10x faster work and learning within their workflow."}, "extension_welcome.faq.q11": {"message": "How can I view and review AI-generated results saved to Memo in the FunBlocks AI browser plugin?"}, "extension_welcome.faq.a11": {"message": "After logging into the FunBlocks AI workspace at https://app.funblocks.net, you can find the Memo page showing previously saved results and related information if you used the 'Save to Memo' feature."}, "extension_welcome.faq.q12": {"message": "Why does the FunBlocks AI browser extension support all major large language models(llms)?"}, "extension_welcome.faq.a12": {"message": "The FunBlocks AI browser extension is designed to provide users with flexible choices and the best experience. By supporting all major large language models, we meet the diverse needs of users, ensuring you can select the most suitable tools based on your personal preferences and workflow, thereby enhancing your work efficiency and creativity."}, "extension_welcome.faq.q13": {"message": "Is the FunBlocks AI browser extension suitable for students?"}, "extension_welcome.faq.a13": {"message": "Yes, the FunBlocks AI browser extension is highly suitable for students. It offers various features to help students improve their learning efficiency. For example, you can use the FunBlocks AI assistant to select words or paragraphs on a webpage for detailed explanations, or engage in critical reading and thinking to enhance cognitive skills. Additionally, the extension supports one-click access to AI Flow, combining mind mapping and AI guidance to explore topics or issues in depth. The writing assistant feature can provide feedback on students' essays, helping to improve writing skills and abilities. Through these features, FunBlocks AI empowers students to be more efficient and comprehensive in their learning process."}, "extension_welcome.faq.q14": {"message": "Is the FunBlocks AI browser extension suitable for workplace use?"}, "extension_welcome.faq.a14": {"message": "Yes, FunBlocks AI is very suitable for workplace use. It provides various features to help professionals enhance their work efficiency. For instance, using the FunBlocks AI browser extension, you can edit text on any webpage, and the AI writing assistant can help draft, polish, correct errors, and translate text, significantly improving writing efficiency. At the same time, FunBlocks AI Flow can assist you in exploring project proposals and breaking down tasks, making complex work simpler."}, "extension_welcome.faq.q15": {"message": "Is the FunBlocks AI browser extension suitable for lifelong learners?"}, "extension_welcome.faq.a15": {"message": "Yes, the FunBlocks AI browser extension is highly suitable for lifelong learners. In an age of information overload, almost everyone needs to enhance their thinking and learning abilities. The FunBlocks AI browser extension meets this need through various features, providing comprehensive critical reading and thinking tools to help users discern information and gain a more holistic perspective. It enhances cognitive skills. Particularly, FunBlocks AI Flow, combined with mind mapping, whiteboarding, and AI solutions, makes users' thinking more fluid and creative, fully unleashing and enhancing their thinking capabilities."}, "extension_welcome.faq.q16": {"message": "Can I use FunBlocks AI Extension for free?"}, "extension_welcome.faq.a16": {"message": "Yes, FunBlocks AI offers a free usage option. New users will receive 30 free AI service accesses upon registration, along with an additional 10 free accesses daily, ensuring you can experience the powerful features of the AI assistant. Additionally, FunBlocks AI has a rewards mechanism; by inviting friends to register, both you and your friends can receive free usage credits."}, "extension_welcome.faq.q17": {"message": "How can I earn FunBlocks AI free usage credits by inviting friends?"}, "extension_welcome.faq.a17": {"message": "You can invite friends in two ways. First, share the invitation link; when your friend registers through the link, they will receive a reward. Second, share content generated by the FunBlocks AI browser extension or AI Flow; if users click to read and register, you will also receive an invitation reward."}, "extension_welcome.faq.q18": {"message": "How can I personalize my FunBlocks AI Extension assistant?"}, "extension_welcome.faq.a18": {"message": "FunBlocks AI offers various personalization options. You can choose from mainstream large language models, build AI prompt applications that meet your needs, and select the required applications from the app market to enhance the functionality of your AI assistant."}, "aidocs.masthead.title": {"message": "Write Smarter, Think Deeper"}, "aidocs.masthead.subtitle": {"message": "Elevate your writing with AI-powered document editor and critical thinking assistance"}, "aidocs.masthead.cta": {"message": "Try for Free"}, "aidocs.intro.title": {"message": "FunBlocks AI Docs: AI-powered Document and Page Editor"}, "aidocs.intro.description": {"message": "FunBlocks AI Docs combines Notion-like editing with AI assistance to enhance your writing, critical thinking, and productivity."}, "aidocs.intro.point1.name": {"message": "Block Editor"}, "aidocs.intro.point1.description": {"message": "Notion-style block editor for intuitive and flexible document creation"}, "aidocs.intro.point2.name": {"message": "AI Writing Assistant"}, "aidocs.intro.point2.description": {"message": "AI writing assistant that helps generate, edit, and improve content"}, "aidocs.intro.point3.name": {"message": "Critical Thinking Enhancement"}, "aidocs.intro.point3.description": {"message": "Critical thinking enhancement to identify biases and logical fallacies"}, "aidocs.intro.point4.name": {"message": "Seamless Integration"}, "aidocs.intro.point4.description": {"message": "Seamless integration with AIFlow mind maps and AI Slides, form all-in-one workflow"}, "aidocs.intro.point5.name": {"message": "Unified Knowledge Workspace"}, "aidocs.intro.point5.description": {"message": "A unified knowledge workspace is formed through links between documents, slides, and mindmaps"}, "aidocs.features.title": {"message": "Key Features"}, "aidocs.features.item1.name": {"message": "Block-Based Editor"}, "aidocs.features.item1.description": {"message": "Create beautiful documents with an intuitive interface that lets you seamlessly mix text, images, tables, code blocks, and more."}, "aidocs.features.item2.name": {"message": "AI Writing Assistant"}, "aidocs.features.item2.description": {"message": "Generate complete documents from scratch, refine existing content, or get intelligent suggestions for improving your writing."}, "aidocs.features.item3.name": {"message": "Infographics Generator"}, "aidocs.features.item3.description": {"message": "Select text within your document and let the AI assistant automatically generate infographics, flowcharts, slides, and more, embedding them at the current position."}, "aidocs.features.item4.name": {"message": "Critical Thinking Enhancement"}, "aidocs.features.item4.description": {"message": "Enhance your reasoning with AI tools that identify cognitive biases, logical fallacies, and weak arguments in your writing."}, "aidocs.features.item5.name": {"message": "Document Organization"}, "aidocs.features.item5.description": {"message": "Create a structured workspace with nested pages, internal linking between documents, and an integrated project management system."}, "aidocs.ai-assistant.title": {"message": "AI Writing Assistant: Your Thinking Partner"}, "aidocs.ai-assistant.subtitle": {"message": "More than just a writing tool - an AI that enhances your critical thinking"}, "aidocs.ai-assistant.critical-thinking.title": {"message": "Enhance Critical Thinking"}, "aidocs.ai-assistant.critical-thinking.description": {"message": "FunBlocks AI Docs goes beyond grammar fixes and style improvements to help you think more clearly and write more persuasively."}, "aidocs.ai-assistant.critical-thinking.point1": {"message": "Identify cognitive biases in your writing"}, "aidocs.ai-assistant.critical-thinking.point2": {"message": "Highlight logical fallacies and suggest improvements"}, "aidocs.ai-assistant.critical-thinking.point3": {"message": "Strengthen arguments by addressing counterpoints"}, "aidocs.ai-assistant.critical-thinking.point4": {"message": "Analyze the clarity and coherence of your reasoning"}, "aidocs.writing-assistant.title": {"message": "Smart Writing Tools"}, "aidocs.writing-assistant.description": {"message": "Create high-quality content efficiently with powerful AI-powered writing tools."}, "aidocs.writing-assistant.point1": {"message": "Generate complete documents from a simple topic or outline"}, "aidocs.writing-assistant.point2": {"message": "Rewrite and refine selected text with targeted instructions"}, "aidocs.writing-assistant.point3": {"message": "Intelligent continuation suggestions based on your writing style"}, "aidocs.writing-assistant.point4": {"message": "Grammar and style correction with detailed explanations"}, "aidocs.writing-assistant.point5": {"message": "Automated creation of visual aids like infographics, flowcharts, and data charts based on content context"}, "aidocs.ecosystem.title": {"message": "Seamless FunBlocks Ecosystem Integration"}, "aidocs.ecosystem.subtitle": {"message": "Part of a powerful AI workspace that enhances your entire workflow"}, "aidocs.ecosystem.workflow.title": {"message": "Complete Workflow Integration"}, "aidocs.ecosystem.workflow.point1": {"message": "Convert AIFlow mind maps directly into structured documents with one click"}, "aidocs.ecosystem.workflow.point2": {"message": "Transform any document into professional slides for presentations"}, "aidocs.ecosystem.workflow.point3": {"message": "Turn document content into visual mind maps for deeper exploration"}, "aidocs.ecosystem.workflow.point4": {"message": "Create visual aids like infographics, flowcharts, and data charts from selected text and insert them into documents"}, "aidocs.ecosystem.workflow.point5": {"message": "Create a comprehensive workspace by linking documents, mind maps and presentations"}, "aidocs.organization.title": {"message": "Powerful Document Organization"}, "aidocs.organization.description": {"message": "Create a structured workspace with linked documents and hierarchical organization"}, "aidocs.organization.point1.name": {"message": "Hierarchical Structure"}, "aidocs.organization.point1.description": {"message": "Organize your documents in a nested structure with folders and subfolders to keep your workspace clean and intuitive."}, "aidocs.organization.point2.name": {"message": "Internal Linking"}, "aidocs.organization.point2.description": {"message": "Connect related documents, mind maps, and presentations through internal links, creating a personal knowledge network."}, "aidocs.organization.point3.name": {"message": "Mixed Content Types"}, "aidocs.organization.point3.description": {"message": "Combine document pages, mind maps, infographics, flowcharts and presentations in a single workspace for a complete project view."}, "aidocs.organization.point4.name": {"message": "Knowledge Workspace"}, "aidocs.organization.point4.description": {"message": "Consolidate work achievements, building an individual or team AI knowledge base."}, "aidocs.use-cases.title": {"message": "Use Cases"}, "aidocs.use-cases.description": {"message": "FunBlocks AI Docs adapts to diverse content creation scenarios, enhancing both productivity and quality"}, "aidocs.use-cases.case1.title": {"message": "Academic Research & Essays"}, "aidocs.use-cases.case1.description": {"message": "Create well-structured academic papers with strong arguments and proper citations, while AI helps identify potential gaps in reasoning."}, "aidocs.use-cases.case2.title": {"message": "Professional Content Creation"}, "aidocs.use-cases.case2.description": {"message": "Produce high-quality blogs, articles, and reports with AI assistance to maintain consistent tone and compelling narratives."}, "aidocs.use-cases.case3.title": {"message": "Learning & Note-Taking"}, "aidocs.use-cases.case3.description": {"message": "Transform class notes into comprehensive study materials with AI-enhanced organization and expansion of key concepts."}, "aidocs.use-cases.case4.title": {"message": "Business Documentation"}, "aidocs.use-cases.case4.description": {"message": "Develop clear business plans, proposals, and reports that present ideas logically and address potential objections."}, "aidocs.use-cases.case5.title": {"message": "Project Planning & Management"}, "aidocs.use-cases.case5.description": {"message": "Create connected workspaces that combine planning documents, visual mind maps, and presentation materials for team alignment."}, "aidocs.use-cases.case6.title": {"message": "Critical Thinking Development"}, "aidocs.use-cases.case6.description": {"message": "Improve your reasoning abilities through regular feedback on your writing, helping you identify and correct flawed thinking patterns."}, "aidocs.testimonials.user1.name": {"message": "<PERSON>"}, "aidocs.testimonials.user1.role": {"message": "Academic Researcher"}, "aidocs.testimonials.user1.text": {"message": "FunBlocks AI Docs revolutionized my research process. The critical thinking enhancement feature identifies potential biases in my academic papers that I would have missed. I can seamlessly transform my initial ideas from mind maps into structured documents, and then into presentation slides for conferences—all within one ecosystem. The AI writing assistant helps refine my arguments and address counterpoints, resulting in more robust research papers. My department colleagues have been amazed at how much more comprehensive and logically sound my work has become since using this tool."}, "aidocs.testimonials.user2.name": {"message": "<PERSON>"}, "aidocs.testimonials.user2.role": {"message": "Content Creator"}, "aidocs.testimonials.user2.text": {"message": "As someone who produces multiple articles weekly, Fun<PERSON><PERSON>s AI Docs has been a game-changer. The block-based editor makes organizing content intuitive, while the AI writing assistant helps me overcome writer's block instantly. What I appreciate most is how it strengthens my arguments by highlighting logical fallacies—my content is now more persuasive and engaging. The seamless integration with mind maps and slides means I can repurpose my written content across different formats, saving hours of work. This tool doesn't just help me write faster; it helps me think deeper and create better content."}, "aidocs.testimonials.user3.name": {"message": "<PERSON>"}, "aidocs.testimonials.user3.role": {"message": "Business Consultant"}, "aidocs.testimonials.user3.text": {"message": "FunBlocks AI Docs has transformed how I create client proposals and reports. The hierarchical document organization keeps all my project materials neatly structured, while the internal linking creates a comprehensive knowledge base I can reference for future work. When developing business strategies, the critical thinking enhancement catches assumptions I might be making, resulting in more thorough analyses. I've received compliments from clients about the clarity and persuasiveness of my recommendations, which I attribute to the AI's ability to help strengthen arguments and address potential objections."}, "aidocs.testimonials.user4.name": {"message": "<PERSON>"}, "aidocs.testimonials.user4.role": {"message": "Graduate Student"}, "aidocs.testimonials.user4.text": {"message": "Taking notes during lectures used to be chaotic, but with FunBlocks AI Docs, I can quickly organize my thoughts in the block editor and let the AI help expand key concepts I might have missed. The ability to transform notes into mind maps helps me see connections between different topics, which has been invaluable for comprehensive exam preparation. The critical thinking features have improved my essay writing dramatically—my professor commented that my arguments have become much more coherent and well-reasoned. This tool has genuinely improved not just my grades, but how I approach learning."}, "aidocs.testimonials.user5.name": {"message": "<PERSON><PERSON>"}, "aidocs.testimonials.user5.role": {"message": "Project Manager"}, "aidocs.testimonials.user5.text": {"message": "Managing complex projects with multiple stakeholders used to be a documentation nightmare until I discovered FunBlocks AI Docs. The unified workspace allows me to link related documents, mind maps for brainstorming, and presentation slides for stakeholder meetings—all interconnected. The AI helps me draft clear project plans and anticipate potential issues by analyzing the logic in my proposals. Team communication has improved dramatically as we now have a centralized knowledge base where everyone can access project information. This tool doesn't just organize information; it enhances our collective thinking process."}, "aidocs.testimonials.user6.name": {"message": "<PERSON>"}, "aidocs.testimonials.user6.role": {"message": "Creative Writer"}, "aidocs.testimonials.user6.text": {"message": "What separates FunBlocks AI Docs from other writing tools is how it enhances my creative process rather than just correcting grammar. The AI writing assistant offers intelligent continuation suggestions that match my style but introduce new perspectives I hadn't considered. The block editor gives me freedom to rearrange sections as my narrative evolves. When developing characters and plot lines, I use the mind map integration to visualize relationships and then transform those ideas into fully developed scenes. For someone who values both creative freedom and structural guidance, this tool offers the perfect balance."}, "aidocs.cta.title": {"message": "Transform Your Thinking, Elevate Your Writing"}, "aidocs.cta.subtitle": {"message": "Join thousands of professionals who've enhanced their creativity, productivity, and critical reasoning with FunBlocks AI Docs"}, "aidocs.cta.button": {"message": "Experience Smarter Writing Today"}, "aidocs.faq.q1": {"message": "What is FunBlocks AI Docs and how does it improve document writing and editing?"}, "aidocs.faq.a1": {"message": "FunBlocks AI Docs is a comprehensive AI-powered document editor that combines Notion-like block editing with advanced artificial intelligence to enhance your writing process, critical thinking abilities, and overall productivity. The platform features a flexible block-based editor similar to Notion, powerful AI writing assistance, critical thinking enhancement tools that identify biases and logical fallacies, and seamless integration with other FunBlocks products like AIFlow mind maps and AI Slides. Whether you're creating academic papers, business documentation, or creative content, FunBlocks AI Docs provides the tools to write smarter and think deeper."}, "aidocs.faq.q2": {"message": "How does the AI Writing Assistant in FunBlocks AI Docs help improve my writing quality and efficiency?"}, "aidocs.faq.a2": {"message": "The AI Writing Assistant in FunBlocks AI Docs functions as your personal thinking partner to elevate both writing quality and efficiency. It provides four key capabilities: 1) Generate complete documents from scratch based on simple topics or outlines, 2) Intelligently rewrite and refine selected text based on specific instructions, 3) Offer smart continuation suggestions that match your personal writing style, and 4) Provide comprehensive grammar and style corrections with detailed explanations. Unlike basic writing tools that only fix grammar, FunBlocks AI Writing Assistant helps you develop clearer thinking, more persuasive arguments, and higher quality content while significantly reducing writing time."}, "aidocs.faq.q3": {"message": "What makes the Critical Thinking Enhancement feature in FunBlocks AI Docs unique compared to other writing tools?"}, "aidocs.faq.a3": {"message": "The Critical Thinking Enhancement feature in FunBlocks AI Docs goes far beyond what traditional writing tools offer by focusing on the quality of your reasoning, not just your writing mechanics. This unique feature helps you think more clearly and write more persuasively through four advanced capabilities: 1) Identifying specific cognitive biases in your writing that might weaken your arguments, 2) Highlighting logical fallacies with clear suggestions for improvement, 3) Strengthening your arguments by identifying and addressing potential counterpoints, and 4) Conducting deep analysis of your reasoning's clarity and coherence. This feature essentially acts as a critical thinking coach, helping you develop stronger analytical skills while producing more logically sound content."}, "aidocs.faq.q4": {"message": "How does FunBlocks AI Docs integrate with AIFlow mind maps and AI Slides to create a complete workflow system?"}, "aidocs.faq.a4": {"message": "FunBlocks AI Docs creates a seamless all-in-one workflow by deeply integrating with AIFlow mind maps and AI Slides through a comprehensive ecosystem approach. This integration enables four powerful workflow capabilities: 1) One-click conversion of AIFlow mind maps directly into structured documents with proper hierarchy and organization, 2) Automatic transformation of any document into professional presentation slides ready for meetings or conferences, 3) Quick conversion of document content into visual mind maps for concept exploration and relationship mapping, and 4) Creation of a unified knowledge workspace through bidirectional linking between documents, mind maps, and presentations. This interconnected system eliminates the need to switch between different applications, allowing ideas to flow naturally through different formats while maintaining content consistency across your entire project."}, "aidocs.faq.q5": {"message": "What document organization and knowledge management features does FunBlocks AI Docs provide for personal and team use?"}, "aidocs.faq.a5": {"message": "FunBlocks AI Docs offers a comprehensive suite of document organization and knowledge management features designed for both personal productivity and team collaboration. The system includes: 1) Advanced hierarchical structure with unlimited nested folders and subfolders for intuitive content categorization, 2) Powerful internal linking capabilities that connect related documents, mind maps, and presentations to create a navigable knowledge network, 3) Support for mixed content types within a single workspace, allowing you to combine document pages, mind maps, and presentations in one view, and 4) Knowledge workspace functionality that consolidates work achievements to build either personal or team-based AI knowledge bases. These features combine to create a structured yet flexible system that grows with your projects and keeps information accessible and contextually connected."}, "aidocs.faq.q6": {"message": "How can academics and researchers use FunBlocks AI Docs to improve research papers and scholarly writing?"}, "aidocs.faq.a6": {"message": "Academics and researchers can leverage FunBlocks AI Docs to significantly enhance their scholarly writing process and research paper quality in multiple ways. The platform helps create well-structured academic papers with logically sound arguments and proper citations, while the AI systematically identifies potential gaps in reasoning or methodological weaknesses. Researchers benefit from the critical thinking enhancement feature that catches cognitive biases and logical fallacies common in academic writing. The mind mapping integration helps visualize complex research relationships and theoretical frameworks before converting them to formal document structures. Additionally, the ability to transform research findings into presentation slides streamlines conference preparation. As confirmed by academic users like <PERSON>, the platform has proven particularly valuable for producing more comprehensive and logically sound research papers that receive positive recognition from academic peers."}, "aidocs.faq.q7": {"message": "What types of business documentation can be created with FunBlocks AI Docs and how does it improve business writing?"}, "aidocs.faq.a7": {"message": "FunBlocks AI Docs is exceptionally well-suited for creating a wide range of business documentation including business plans, project proposals, market analysis reports, strategic recommendations, client presentations, and internal process documentation. The platform improves business writing by helping professionals develop clear, logical documents that present ideas persuasively and proactively address potential objections or weaknesses. The unified workspace facilitates comprehensive project planning and management by combining strategic planning documents, visual mind maps for brainstorming, and presentation materials for stakeholder alignment. Business users like <PERSON>, a business consultant, report receiving client compliments on the clarity and persuasiveness of their recommendations since using the platform. The critical thinking enhancement feature is particularly valuable for business strategy development, as it helps identify unstated assumptions and logical gaps that might otherwise undermine business proposals."}, "aidocs.faq.q8": {"message": "How does the Notion-style block-based editor in FunBlocks AI Docs work and what content types does it support?"}, "aidocs.faq.a8": {"message": "The Notion-style block-based editor in FunBlocks AI Docs provides an intuitive, flexible interface for document creation through a modular building-block approach. Each piece of content exists as a distinct block that can be easily moved, edited, or transformed. The editor supports a comprehensive range of content types including rich text with multiple formatting options, headings and subheadings, bulleted and numbered lists, tables with sorting and filtering capabilities, embedded images and media, code blocks with syntax highlighting for multiple programming languages, mathematical equations, callout blocks for important information, toggle lists for expandable content, and embedded files. This block-based approach allows for easy reorganization through drag-and-drop functionality, consistent formatting throughout documents, and the ability to quickly convert between block types. The system combines the flexibility of a word processor with the structure of a database, making it ideal for creating beautifully organized documents of any complexity."}, "aidocs.faq.q9": {"message": "How can students and lifelong learners use FunBlocks AI Docs for better note-taking, study materials, and knowledge retention?"}, "aidocs.faq.a9": {"message": "Students and lifelong learners can transform their educational experience with FunBlocks AI Docs through several powerful learning applications. The platform excels at helping users convert quick lecture notes into comprehensive study materials through AI-enhanced organization and intelligent expansion of key concepts. Students like <PERSON> have found that the ability to transform linear notes into visual mind maps helps identify connections between different topics, significantly improving exam preparation and conceptual understanding. The block editor allows for flexible organization of study materials with mixed media types, while the <PERSON> assistant can help clarify difficult concepts or generate explanations for complex topics. The critical thinking features have proven particularly valuable for essay writing, helping students develop more coherent, well-reasoned arguments that professors notice and reward. Beyond formal education, the platform supports lifelong learning by creating a structured knowledge base that grows over time, connecting related concepts across different subjects and identifying patterns that might otherwise remain hidden."}, "aidocs.faq.q10": {"message": "Is there a free trial of FunBlocks AI Docs available and what features are included in different pricing plans?"}, "aidocs.faq.a10": {"message": "Yes, FunBlocks AI Docs offers a free trial version as indicated by the prominent 'Try for Free' call-to-action on the website. This trial allows new users to experience the platform's core features and evaluate how it fits their writing and thinking needs before committing to a paid subscription. While specific pricing details aren't provided in the current information, the free trial likely includes access to the basic block editor functionality, limited AI writing assistance, and a sample of the critical thinking enhancement features. For comprehensive information about different pricing tiers, feature limitations in the free version, usage quotas, and enterprise options, we recommend visiting the official FunBlocks pricing page. As with many productivity tools, paid subscriptions likely offer additional benefits such as increased usage limits, priority support, team collaboration features, and access to advanced AI capabilities."}, "aidocs.faq.q11": {"message": "How does FunBlocks AI Docs help content creators, bloggers, and professional writers improve their content quality and production efficiency?"}, "aidocs.faq.a11": {"message": "FunBlocks AI Docs offers significant advantages for content creators, bloggers, and professional writers who need to maintain both quality and volume in their production. As experienced by <PERSON>, a professional content creator, the platform helps produce high-quality blogs, articles, reports, and creative content with AI assistance that maintains consistent tone and creates compelling narratives across multiple pieces. The AI writing assistant is particularly valuable for overcoming writer's block through intelligent suggestions and draft generation, while the critical thinking features strengthen arguments by highlighting logical fallacies, making content more persuasive and engaging for readers. Content professionals benefit from the ability to quickly repurpose written content across different formats (documents, mind maps, and presentations), significantly reducing production time for multi-channel content strategies. The block editor's intuitive organization makes managing multiple related content pieces simpler, while creative writers like <PERSON> appreciate how the system enhances the creative process by offering continuation suggestions that match their unique style while introducing fresh perspectives they hadn't considered."}, "aidocs.faq.q12": {"message": "How can I get started with FunBlocks AI Docs?"}, "aidocs.faq.a12": {"message": "Simply click the 'Free Trial' button, create an account, and you can immediately start using FunBlocks AI for free. No credit card is required for the trial, and you'll have access to all features."}, "aidocs.head.title": {"message": "FunBlocks AI Docs: AI-powered Block Editor & Critical Thinking Assistant | Brainstorming & Mind Mapping"}, "aidocs.head.description": {"message": "FunBlocks AI Docs combines Notion-like editing with AI assistance to enhance your writing, critical thinking, and brainstorming capabilities. Create better content faster with integrated mind mapping and creative thinking tools."}, "aidocs.comparison.title": {"message": "How FunBlocks AI Docs Compares"}, "aidocs.comparison.description": {"message": "FunBlocks AI Docs combines the best features of document editors, AI writing assistants, and creative thinking tools into one seamless platform"}, "aidocs.comparison.funblocksHeader": {"message": "FunBlocks AI Docs"}, "aidocs.comparison.googleDocsHeader": {"message": "Google Docs"}, "aidocs.comparison.notionHeader": {"message": "Notion"}, "aidocs.comparison.grammarly": {"message": "Grammarly"}, "aidocs.comparison.feature1": {"message": "Block-Based Editor"}, "aidocs.comparison.feature2": {"message": "AI Writing Assistant"}, "aidocs.comparison.feature3": {"message": "Critical Thinking Enhancement"}, "aidocs.comparison.feature4": {"message": "Brainstorming Tools"}, "aidocs.comparison.feature5": {"message": "Mind Map Integration"}, "aidocs.comparison.feature6": {"message": "Infographics Generation"}, "aidocs.comparison.feature7": {"message": "Creative Thinking Tools"}, "aidocs.comparison.feature8": {"message": "Document Organization"}, "aidocs.comparison.note": {"message": "FunBlocks AI Docs provides a comprehensive solution that combines document editing, AI assistance, and creative thinking tools in one platform, eliminating the need to switch between multiple applications."}, "aidocs.stats.title": {"message": "Trusted by Students and Professionals Worldwide"}, "aidocs.stats.description": {"message": "Join thousands of professionals who use FunBlocks AI Docs to enhance their writing and thinking"}, "aidocs.stats.users": {"message": "Active Users"}, "aidocs.stats.documents": {"message": "Documents Created"}, "aidocs.stats.timeSaved": {"message": "Average Time Saved"}, "aidocs.stats.satisfaction": {"message": "User Satisfaction"}, "aidocs.thinking-enhancement.title": {"message": "Writing as a Thinking Tool"}, "aidocs.thinking-enhancement.description": {"message": "Writing is not just for output—it's a powerful method for learning and enhancing cognitive abilities. FunBlocks AI ecosystem provides a complete solution for your thinking journey."}, "aidocs.thinking-enhancement.cycle.title": {"message": "The Complete Thinking Cycle"}, "aidocs.thinking-enhancement.cycle.description": {"message": "FunBlocks AI integrates all stages of the thinking process—from initial ideas to structured knowledge—creating a powerful ecosystem for learning and intellectual growth."}, "aidocs.thinking-enhancement.cycle.step1.title": {"message": "Explore & Discover"}, "aidocs.thinking-enhancement.cycle.step1.description": {"message": "Begin with AI-powered brainstorming to explore topics and generate initial ideas"}, "aidocs.thinking-enhancement.cycle.step2.title": {"message": "Visualize & Connect"}, "aidocs.thinking-enhancement.cycle.step2.description": {"message": "Organize thoughts with mind maps to see relationships and identify patterns"}, "aidocs.thinking-enhancement.cycle.step3.title": {"message": "Create & Develop"}, "aidocs.thinking-enhancement.cycle.step3.description": {"message": "Transform ideas into structured documents with creative thinking tools"}, "aidocs.thinking-enhancement.cycle.step4.title": {"message": "Analyze & Refine"}, "aidocs.thinking-enhancement.cycle.step4.description": {"message": "Apply critical thinking to strengthen arguments and eliminate biases"}, "aidocs.thinking-enhancement.tools.title": {"message": "AI-Powered Thinking Tools"}, "aidocs.thinking-enhancement.tools.brainstorming": {"message": "Generate ideas using AI-guided frameworks like SCAMPER, Six Thinking Hats, and First Principles thinking. Expand seed concepts into comprehensive outlines."}, "aidocs.thinking-enhancement.tools.mindmap": {"message": "Visualize complex relationships between ideas with one-click conversion between documents and mind maps. Enhance comprehension and retention through visual thinking."}, "aidocs.thinking-enhancement.tools.creative": {"message": "View content from multiple perspectives, generate analogies and metaphors, and challenge limiting assumptions to discover innovative solutions and insights."}, "aidocs.thinking-enhancement.tools.critical.title": {"message": "Critical Thinking Assistant"}, "aidocs.thinking-enhancement.tools.critical": {"message": "Identify cognitive biases, highlight logical fallacies, strengthen arguments by addressing counterpoints, and analyze the clarity of your reasoning."}, "aidocs.thinking-enhancement.quote": {"message": "\"Writing is not just communicating ideas; it's a powerful tool for developing them. FunBlocks AI transforms writing from mere documentation into an active learning process.\""}, "prompt_optimizer.less_is_more.title": {"message": "Get More from AI with Less Instructions"}, "prompt_optimizer.less_is_more.description": {"message": "Sometimes less is more when it comes to AI prompts. Start with your goal and let AI do the heavy lifting."}, "prompt_optimizer.less_is_more.point1.title": {"message": "Focus on Your Goal"}, "prompt_optimizer.less_is_more.point1.description": {"message": "Instead of detailed instructions, simply state what you want to achieve. Let AI analyze and plan the best approach."}, "prompt_optimizer.less_is_more.point2.title": {"message": "Break Free from Limitations"}, "prompt_optimizer.less_is_more.point2.description": {"message": "Detailed instructions can limit AI's creativity. By focusing on goals, you open doors to solutions you might not have considered."}, "prompt_optimizer.less_is_more.point3.title": {"message": "Let AI Do the Heavy Lifting"}, "prompt_optimizer.less_is_more.point3.description": {"message": "The Prompt Optimizer helps you start with simple goals, then uses AI to expand and enhance your prompts for better results."}, "prompt_optimizer.less_is_more.cta": {"message": "Experience how less can truly be more with the Prompt Optimizer"}, "prompt_optimizer.less_is_more.button": {"message": "Try It Now"}, "ai101.slide19.paradigm.title": {"message": "Paradigm Shift"}, "ai101.slide19.paradigm.traditional": {"message": "Traditional Era"}, "ai101.slide19.paradigm.generative": {"message": "Generative Era"}, "ai101.slide19.paradigm.traditional.item1": {"message": "Information Scarcity"}, "ai101.slide19.paradigm.generative.item1": {"message": "Information Abundance"}, "ai101.slide19.paradigm.traditional.item2": {"message": "Content Creation is Hard"}, "ai101.slide19.paradigm.generative.item2": {"message": "Content Creation is Easy"}, "ai101.slide19.paradigm.traditional.item3": {"message": "Focus on Memorization"}, "ai101.slide19.paradigm.generative.item3": {"message": "Focus on Critical Evaluation"}, "ai101.slide19.paradigm.traditional.item4": {"message": "Individual Learning"}, "ai101.slide19.paradigm.generative.item4": {"message": "AI-Assisted Learning"}, "ai101.slide19.paradigm.traditional.item5": {"message": "Linear Curriculum"}, "ai101.slide19.paradigm.generative.item5": {"message": "Personalized Pathways"}, "ai101.slide19.priorities.title": {"message": "New Learning Priorities"}, "ai101.slide19.priorities.item1": {"message": "Develop information literacy and source evaluation skills"}, "ai101.slide19.priorities.item2": {"message": "Learn to collaborate effectively with AI tools"}, "ai101.slide19.priorities.item3": {"message": "Focus on creativity, critical thinking, and problem-solving"}, "ai101.slide19.priorities.item4": {"message": "Cultivate emotional intelligence and human connection"}, "prompt_optimizer.lazy_prompting.title": {"message": "Lazy Prompting: A New Approach to AI Interaction"}, "prompt_optimizer.lazy_prompting.description": {"message": "Lazy prompting is an innovative method that starts with minimal context and gradually adds more as needed, allowing AI to surprise us with unexpected insights and solutions."}, "prompt_optimizer.lazy_prompting.point1.title": {"message": "Start Simple"}, "prompt_optimizer.lazy_prompting.point1.description": {"message": "Begin with a basic idea or question, letting the AI explore possibilities without being constrained by detailed instructions."}, "prompt_optimizer.lazy_prompting.point2.title": {"message": "Discover the Unexpected"}, "prompt_optimizer.lazy_prompting.point2.description": {"message": "Allow AI to surprise you with solutions and perspectives you might not have considered with traditional prompting methods."}, "prompt_optimizer.lazy_prompting.point3.title": {"message": "Optimize as Needed"}, "prompt_optimizer.lazy_prompting.point3.description": {"message": "Use the Prompt Optimizer to enhance and refine your prompts when you need more specific guidance or detailed responses."}, "prompt_optimizer.lazy_prompting.cta": {"message": "Unlock the power of lazy prompting with FunBlocks AI Prompt Optimizer. Get high-quality AI answers efficiently, while enjoying all the benefits of lazy prompting."}, "prompt_optimizer.lazy_prompting.button": {"message": "Try It Now"}, "prompt_optimizer.thinking_enhancement.title": {"message": "Beyond Prompt Optimization: Enhancing Your Thinking Abilities"}, "prompt_optimizer.thinking_enhancement.description": {"message": "Our extension doesn't just optimize prompts—it actively develops your critical thinking skills and maximizes AI value through enhanced human-AI collaboration. Thinking matters in the era of AI."}, "prompt_optimizer.thinking_enhancement.point1.title": {"message": "Master the Art of Asking Better Questions"}, "prompt_optimizer.thinking_enhancement.point1.description": {"message": "Good questions are the key to solving problems. Questioning ability is a crucial human thinking skill. Our AI helps you optimize your questions and practice this essential skill through guided improvement."}, "prompt_optimizer.thinking_enhancement.point2.title": {"message": "Clearer Problem Description"}, "prompt_optimizer.thinking_enhancement.point2.description": {"message": "Learn to describe problems more clearly and precisely through guided optimization, developing better communication skills that benefit all your AI interactions."}, "prompt_optimizer.thinking_enhancement.point3.title": {"message": "Broader Perspective Exploration"}, "prompt_optimizer.thinking_enhancement.point3.description": {"message": "Generate related questions and topics based on your input or AI responses, encouraging you to explore subjects from multiple angles and discover new insights."}, "prompt_optimizer.thinking_enhancement.point4.title": {"message": "Critical Analysis Skills"}, "prompt_optimizer.thinking_enhancement.point4.description": {"message": "Perform critical analysis on AI responses to improve your analytical and judgment abilities, helping you evaluate information quality and identify potential biases."}, "prompt_optimizer.thinking_enhancement.cta.title": {"message": "Enhance Your Thinking, Not Replace It"}, "prompt_optimizer.thinking_enhancement.cta.description": {"message": "After using our extension, you'll not only get better results from ChatGPT, Claude, and other AI tools, but also develop stronger thinking abilities. Because thinking matters in the era of AI."}, "prompt_optimizer.thinking_enhancement.cta.button": {"message": "Start Thinking Better with AI"}}